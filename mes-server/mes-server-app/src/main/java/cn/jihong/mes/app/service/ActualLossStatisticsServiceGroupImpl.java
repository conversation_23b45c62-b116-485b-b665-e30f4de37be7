package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.OssUtils;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupDTO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesGroupDetailVO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesGroupVO;
import cn.jihong.mes.api.service.IActualLossStatisticsGroupService;
import cn.jihong.mes.app.property.AliyunOssProperty;
import cn.jihong.mes.app.util.TimeTypeUtil;
import cn.jihong.mes.production.api.model.enums.SiteEnum;
import cn.jihong.oa.erp.api.model.dto.ActualLossStatisticsDTO;
import cn.jihong.oa.erp.api.model.vo.ActualLossStatisticsGroupVO;
import cn.jihong.oa.erp.api.model.vo.ReturnMaterialVO;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import cn.jihong.oa.erp.api.service.ISffbTService;
import cn.jihong.oa.erp.api.service.ISiteViewService;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
public class ActualLossStatisticsServiceGroupImpl implements IActualLossStatisticsGroupService {
    @DubboReference(timeout = 180000 )
    private ISffbTService sffbTService;
    @DubboReference(timeout = 180000 )
    private ISfcbTService sfcbTService;
    @Autowired
    private RedisTemplate redisTemplate;
    @DubboReference(timeout = 180000 )
    private ISiteViewService siteViewService;
    @Resource
    private AliyunOssProperty aliyunOssProperty;
    @Override
    public List<ActualLossStatisticsMesGroupVO> selectActualLosss(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        if (actualLossStatisticsParamGroupDTO == null){
            throw new CommonException("请求参数不能为空");
        }
        if (StringUtil.isEmpty(actualLossStatisticsParamGroupDTO.getStartDate())){
            throw new CommonException("开始日期不能为空");
        }else {
            if(!TimeTypeUtil.isLegalDate(actualLossStatisticsParamGroupDTO.getStartDate().length(), actualLossStatisticsParamGroupDTO.getStartDate(), "yyyy-MM-dd")){
                throw new CommonException("开始日期不符合格式：yyyy-MM-dd");
            }
        }
        if (StringUtil.isEmpty(actualLossStatisticsParamGroupDTO.getEndDate())){
            throw new CommonException("结束日期不能为空");
        }else {
            if(!TimeTypeUtil.isLegalDate(actualLossStatisticsParamGroupDTO.getEndDate().length(), actualLossStatisticsParamGroupDTO.getEndDate(), "yyyy-MM-dd")){
                throw new CommonException("结束日期不符合格式：yyyy-MM-dd");
            }
        }

        ActualLossStatisticsDTO lossStatisticsDTO = new ActualLossStatisticsDTO();
        if ("3".equals(actualLossStatisticsParamGroupDTO.getStatisticalCategory())){//半成品只显示安徽吉宏
            lossStatisticsDTO.setFactoryCode(SiteEnum.AHGC.getCode());
        }else {
            lossStatisticsDTO.setFactoryCode(actualLossStatisticsParamGroupDTO.getFactoryCode());
        }
        lossStatisticsDTO.setStatisticalCategory(actualLossStatisticsParamGroupDTO.getStatisticalCategory());
        Date startDate = DateUtil.parse(actualLossStatisticsParamGroupDTO.getStartDate(),"yyyy-MM-dd");
        Date endDate = DateUtil.offsetDay(DateUtil.parse(actualLossStatisticsParamGroupDTO.getEndDate(),"yyyy-MM-dd"),1);
        lossStatisticsDTO.setStartDate(DateUtil.format(startDate,"yyyy-MM-dd"));
        lossStatisticsDTO.setEndDate(DateUtil.format(endDate,"yyyy-MM-dd"));
        lossStatisticsDTO.setProductCategoryNo(actualLossStatisticsParamGroupDTO.getProductCategoryNo());
        lossStatisticsDTO.setFactoryType(actualLossStatisticsParamGroupDTO.getFactoryType());
        lossStatisticsDTO.setProcessNo(actualLossStatisticsParamGroupDTO.getProcessNo());

        List<ActualLossStatisticsGroupVO> actualLossStatisticsVOS = sffbTService.selectActualLosssGroupDetail(lossStatisticsDTO);
        List<ReturnMaterialVO> returnMaterialVOS = sfcbTService.getReturnMaterial(lossStatisticsDTO.getStartDate(),lossStatisticsDTO.getEndDate());
        if (CollectionUtil.isNotEmpty(actualLossStatisticsVOS)){
            //计算损耗率
            BigDecimal zero = new BigDecimal("0");
            Map<String,ActualLossStatisticsGroupVO> actualLossStatisticsGroupVOMap = new HashMap<>();
            //根据据点、工单号、工序编码为唯一值，保存为map对象
            for (ActualLossStatisticsGroupVO actualLossStatisticsVO : actualLossStatisticsVOS) {
                actualLossStatisticsGroupVOMap.put(actualLossStatisticsVO.getFactoryNo()+actualLossStatisticsVO.getWorkOrderNO()+actualLossStatisticsVO.getProcessNo(),actualLossStatisticsVO);
            }
            List<ActualLossStatisticsGroupVO> actualLossStatisticsGroupVOS = new ArrayList<>();
            for (ActualLossStatisticsGroupVO actualLossStatisticsGroupVO:actualLossStatisticsVOS){
                if ("INIT".equals(actualLossStatisticsGroupVO.getLastProcess())){
                    ActualLossStatisticsGroupVO actualLossStatisticsGroupVONew = new ActualLossStatisticsGroupVO();
                    actualLossStatisticsGroupVONew.setFactoryNo(actualLossStatisticsGroupVO.getFactoryNo());
                    actualLossStatisticsGroupVONew.setFactoryName(actualLossStatisticsGroupVO.getFactoryName());
                    actualLossStatisticsGroupVONew.setCategory(actualLossStatisticsGroupVO.getCategory());
                    actualLossStatisticsGroupVONew.setProcessNo(actualLossStatisticsGroupVO.getProcessNo());
                    actualLossStatisticsGroupVONew.setProcessName(actualLossStatisticsGroupVO.getProcessName());
                    actualLossStatisticsGroupVONew.setWorkOrderNO(actualLossStatisticsGroupVO.getWorkOrderNO());
                    actualLossStatisticsGroupVONew.setLastProcess(actualLossStatisticsGroupVO.getLastProcess());
                    actualLossStatisticsGroupVONew.setNextProcess(actualLossStatisticsGroupVO.getNextProcess());
                    actualLossStatisticsGroupVONew.setInitDefectiveProductNum(actualLossStatisticsGroupVO.getInitDefectiveProductNum());
                    actualLossStatisticsGroupVONew.setQualifiedQuantity(actualLossStatisticsGroupVO.getQualifiedQuantity());
                    actualLossStatisticsGroupVONew.setWorkOrderQuantity(actualLossStatisticsGroupVO.getWorkOrderQuantity());
                    actualLossStatisticsGroupVONew.setVirtualReporteQuantity(actualLossStatisticsGroupVO.getVirtualReporteQuantity());
                    actualLossStatisticsGroupVONew.setDevelopmentReturns(actualLossStatisticsGroupVO.getDevelopmentReturns());
                    actualLossStatisticsGroupVONew.setCustomerOfflineQuantity(actualLossStatisticsGroupVO.getCustomerOfflineQuantity());
                    actualLossStatisticsGroupVONew.setInitReportingNum(actualLossStatisticsGroupVO.getInitReportingNum());
                    actualLossStatisticsGroupVONew.setBillingQuantity(actualLossStatisticsGroupVO.getBillingQuantity());
                    actualLossStatisticsGroupVONew.setInitBillingLossQuantity(actualLossStatisticsGroupVO.getInitBillingLossQuantity());
                    actualLossStatisticsGroupVONew.setInitStandardLossQuantity(actualLossStatisticsGroupVO.getInitStandardLossQuantity());
                    actualLossStatisticsGroupVONew.setInitRatedLossQuantity(actualLossStatisticsGroupVO.getInitRatedLossQuantity());
                    String nextProcess = actualLossStatisticsGroupVO.getNextProcess();
                    BigDecimal standardLossQuantity = actualLossStatisticsGroupVO.getStandardLossQuantity();
                    BigDecimal ratedLossQuantity = actualLossStatisticsGroupVO.getRatedLossQuantity();
                    BigDecimal billingLossQuantity = actualLossStatisticsGroupVO.getBillingLossQuantity();
                    BigDecimal endReportingNum = actualLossStatisticsGroupVO.getEndReportingNum();
                    //假如是纸板的理论，抄瓦首工序理论损耗置为0，实际不良置为0
//                    if("纸板".equals(actualLossStatisticsGroupVO.getCategory())){
//                        actualLossStatisticsGroupVONew.setInitDefectiveProductNum(new BigDecimal(0));
//                        standardLossQuantity = new BigDecimal(0);
//                        ratedLossQuantity = new BigDecimal(0);
//                        billingLossQuantity = new BigDecimal(0);
//                    }
                    while (nextProcess != null && !nextProcess.equals("") && !nextProcess.equals("END")){
                        ActualLossStatisticsGroupVO voMap =actualLossStatisticsGroupVOMap.get(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getWorkOrderNO()+nextProcess);
                        log.debug("voMap is : "+voMap+"and standardLossQuantity is :"+standardLossQuantity);
                        if (voMap != null){
                            standardLossQuantity = (standardLossQuantity==null?new BigDecimal(0):standardLossQuantity).add(voMap.getStandardLossQuantity()==null?new BigDecimal(0):voMap.getStandardLossQuantity());
                            ratedLossQuantity = (ratedLossQuantity==null?new BigDecimal(0):ratedLossQuantity).add(voMap.getRatedLossQuantity()==null?new BigDecimal(0):voMap.getRatedLossQuantity());
                            billingLossQuantity = (billingLossQuantity==null?new BigDecimal(0):billingLossQuantity).add(voMap.getBillingLossQuantity()==null?new BigDecimal(0):voMap.getBillingLossQuantity());
                            endReportingNum = (endReportingNum==null?new BigDecimal(0):endReportingNum).add(voMap.getEndReportingNum()==null?new BigDecimal(0):voMap.getEndReportingNum());
                            nextProcess = voMap.getNextProcess();
                        }else {
                            nextProcess = "END";
                        }
                    }
                    actualLossStatisticsGroupVONew.setStandardLossQuantity(standardLossQuantity);
                    actualLossStatisticsGroupVONew.setRatedLossQuantity(ratedLossQuantity);
                    actualLossStatisticsGroupVONew.setBillingLossQuantity(billingLossQuantity);
                    actualLossStatisticsGroupVONew.setEndReportingNum(endReportingNum);
                    actualLossStatisticsGroupVOS.add(actualLossStatisticsGroupVONew);
                }
            }
            Map<String,ActualLossStatisticsGroupVO> totalMap = new HashMap<>();
            //退料map
            Map<String,ReturnMaterialVO> returnMaterialVOMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(returnMaterialVOS)){
                for (ReturnMaterialVO returnMaterialVO:returnMaterialVOS){
                    returnMaterialVOMap.put(returnMaterialVO.getFactoryNo()+returnMaterialVO.getWorkOrderNO()+returnMaterialVO.getProcessNo()+returnMaterialVO.getCategory(),returnMaterialVO);
                }
            }
            for (ActualLossStatisticsGroupVO actualLossStatisticsGroupVO:actualLossStatisticsGroupVOS){
                ActualLossStatisticsGroupVO lossStatisticsGroupVO = totalMap.get(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getCategory());
                ReturnMaterialVO returnMaterialVO = returnMaterialVOMap.get(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getWorkOrderNO()+actualLossStatisticsGroupVO.getProcessNo()+actualLossStatisticsGroupVO.getCategory());
                if (returnMaterialVO != null){
                    actualLossStatisticsGroupVO.setDevelopmentReturns(returnMaterialVO.getReturnQuantity());
//                    actualLossStatisticsGroupVO.setInitReportingNum(actualLossStatisticsGroupVO.getInitReportingNum().subtract(returnMaterialVO.getReturnQuantity()));
                }
//                if (actualLossStatisticsGroupVO.getVirtualReporteQuantity() != null){
//                    actualLossStatisticsGroupVO.setInitReportingNum(actualLossStatisticsGroupVO.getInitReportingNum().add(actualLossStatisticsGroupVO.getVirtualReporteQuantity()));
//                }
                if (lossStatisticsGroupVO != null){
                    BigDecimal initDefectiveProductNum = actualLossStatisticsGroupVO.getInitDefectiveProductNum();
                    BigDecimal virtualReporteQuantity = actualLossStatisticsGroupVO.getVirtualReporteQuantity();
                    BigDecimal developmentReturns = (actualLossStatisticsGroupVO.getDevelopmentReturns()==null?new BigDecimal("0"):actualLossStatisticsGroupVO.getDevelopmentReturns());
                    BigDecimal customerOfflineQuantity = actualLossStatisticsGroupVO.getCustomerOfflineQuantity();
                    BigDecimal initReportingNum = actualLossStatisticsGroupVO.getInitReportingNum();
                    BigDecimal qualifiedQuantity = actualLossStatisticsGroupVO.getQualifiedQuantity();
                    BigDecimal billingQuantity = actualLossStatisticsGroupVO.getBillingQuantity();
                    BigDecimal standardLossQuantity = actualLossStatisticsGroupVO.getStandardLossQuantity();
                    BigDecimal ratedLossQuantity = actualLossStatisticsGroupVO.getRatedLossQuantity();
                    BigDecimal billingLossQuantity = actualLossStatisticsGroupVO.getBillingLossQuantity();
                    BigDecimal workOrderQuantity = actualLossStatisticsGroupVO.getWorkOrderQuantity();
                    BigDecimal initBillingLossQuantity = actualLossStatisticsGroupVO.getInitBillingLossQuantity();
                    BigDecimal initStandardLossQuantity = actualLossStatisticsGroupVO.getInitStandardLossQuantity();
                    BigDecimal initRatedLossQuantity = actualLossStatisticsGroupVO.getInitRatedLossQuantity();
                    BigDecimal endReportingNum = actualLossStatisticsGroupVO.getEndReportingNum();
                    initDefectiveProductNum = initDefectiveProductNum.add(lossStatisticsGroupVO.getInitDefectiveProductNum());
                    virtualReporteQuantity = virtualReporteQuantity.add(lossStatisticsGroupVO.getVirtualReporteQuantity());
                    developmentReturns = developmentReturns.add(lossStatisticsGroupVO.getDevelopmentReturns()==null?new BigDecimal("0"):lossStatisticsGroupVO.getDevelopmentReturns());
                    customerOfflineQuantity = customerOfflineQuantity.add(lossStatisticsGroupVO.getCustomerOfflineQuantity());
                    initReportingNum = initReportingNum.add(lossStatisticsGroupVO.getInitReportingNum());
                    qualifiedQuantity = qualifiedQuantity.add(lossStatisticsGroupVO.getQualifiedQuantity());
                    billingQuantity = billingQuantity.add(lossStatisticsGroupVO.getBillingQuantity());
                    standardLossQuantity = standardLossQuantity.add(lossStatisticsGroupVO.getStandardLossQuantity());
                    ratedLossQuantity = ratedLossQuantity.add(lossStatisticsGroupVO.getRatedLossQuantity());
                    billingLossQuantity = billingLossQuantity.add(lossStatisticsGroupVO.getBillingLossQuantity());
                    workOrderQuantity = workOrderQuantity.add(lossStatisticsGroupVO.getWorkOrderQuantity());
                    initBillingLossQuantity = initBillingLossQuantity.add(lossStatisticsGroupVO.getInitBillingLossQuantity());
                    endReportingNum = endReportingNum.add(lossStatisticsGroupVO.getEndReportingNum());
//                    if (initStandardLossQuantity == null){
//                        System.out.println("测试1"+lossStatisticsGroupVO);
//                        System.out.println("测试2"+actualLossStatisticsGroupVO);
//                    }
                    initStandardLossQuantity = initStandardLossQuantity.add(lossStatisticsGroupVO.getInitStandardLossQuantity());
                    initRatedLossQuantity = initRatedLossQuantity.add(lossStatisticsGroupVO.getInitRatedLossQuantity());
                    lossStatisticsGroupVO.setInitDefectiveProductNum(initDefectiveProductNum);
                    lossStatisticsGroupVO.setVirtualReporteQuantity(virtualReporteQuantity);
                    lossStatisticsGroupVO.setDevelopmentReturns(developmentReturns);
                    lossStatisticsGroupVO.setCustomerOfflineQuantity(customerOfflineQuantity);
                    lossStatisticsGroupVO.setInitReportingNum(initReportingNum);
                    lossStatisticsGroupVO.setQualifiedQuantity(qualifiedQuantity);
                    lossStatisticsGroupVO.setBillingQuantity(billingQuantity);
                    lossStatisticsGroupVO.setStandardLossQuantity(standardLossQuantity);
                    lossStatisticsGroupVO.setRatedLossQuantity(ratedLossQuantity);
                    lossStatisticsGroupVO.setBillingLossQuantity(billingLossQuantity);
                    lossStatisticsGroupVO.setWorkOrderQuantity(workOrderQuantity);
                    lossStatisticsGroupVO.setInitBillingLossQuantity(initBillingLossQuantity);
                    lossStatisticsGroupVO.setInitStandardLossQuantity(initStandardLossQuantity);
                    lossStatisticsGroupVO.setInitRatedLossQuantity(initRatedLossQuantity);
                    lossStatisticsGroupVO.setEndReportingNum(endReportingNum);
                    totalMap.put(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getCategory(),lossStatisticsGroupVO);
                }else {
                    ActualLossStatisticsGroupVO statisticsGroupVO = new ActualLossStatisticsGroupVO();
                    statisticsGroupVO.setFactoryNo(actualLossStatisticsGroupVO.getFactoryNo());
                    statisticsGroupVO.setFactoryName(actualLossStatisticsGroupVO.getFactoryName());
                    statisticsGroupVO.setCategory(actualLossStatisticsGroupVO.getCategory());
                    statisticsGroupVO.setInitDefectiveProductNum(actualLossStatisticsGroupVO.getInitDefectiveProductNum());
                    statisticsGroupVO.setVirtualReporteQuantity(actualLossStatisticsGroupVO.getVirtualReporteQuantity());
                    statisticsGroupVO.setDevelopmentReturns(actualLossStatisticsGroupVO.getDevelopmentReturns()==null?new BigDecimal("0"):actualLossStatisticsGroupVO.getDevelopmentReturns());
                    statisticsGroupVO.setCustomerOfflineQuantity(actualLossStatisticsGroupVO.getCustomerOfflineQuantity());
                    statisticsGroupVO.setInitReportingNum(actualLossStatisticsGroupVO.getInitReportingNum());
                    statisticsGroupVO.setQualifiedQuantity(actualLossStatisticsGroupVO.getQualifiedQuantity());
                    statisticsGroupVO.setBillingQuantity(actualLossStatisticsGroupVO.getBillingQuantity());
                    statisticsGroupVO.setStandardLossQuantity(actualLossStatisticsGroupVO.getStandardLossQuantity());
                    statisticsGroupVO.setRatedLossQuantity(actualLossStatisticsGroupVO.getRatedLossQuantity());
                    statisticsGroupVO.setBillingLossQuantity(actualLossStatisticsGroupVO.getBillingLossQuantity());
                    statisticsGroupVO.setWorkOrderQuantity(actualLossStatisticsGroupVO.getWorkOrderQuantity());
                    statisticsGroupVO.setInitBillingLossQuantity(actualLossStatisticsGroupVO.getInitBillingLossQuantity());
                    statisticsGroupVO.setInitRatedLossQuantity(actualLossStatisticsGroupVO.getInitRatedLossQuantity());
                    statisticsGroupVO.setInitStandardLossQuantity(actualLossStatisticsGroupVO.getInitStandardLossQuantity());
                    statisticsGroupVO.setEndReportingNum(actualLossStatisticsGroupVO.getEndReportingNum());
                    totalMap.put(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getCategory(),statisticsGroupVO);
                }
            }

            List<ActualLossStatisticsMesGroupVO> actualLossStatisticsMesGroupVOS = new ArrayList<>();
            for (Map.Entry<String, ActualLossStatisticsGroupVO> entry : totalMap.entrySet()) {
                String key = entry.getKey();
                ActualLossStatisticsGroupVO value = entry.getValue();
                // 在这里处理键和值
                ActualLossStatisticsMesGroupVO actualLossStatisticsMesGroupVO = new ActualLossStatisticsMesGroupVO();
                actualLossStatisticsMesGroupVO.setFactoryName(value.getFactoryName());
                actualLossStatisticsMesGroupVO.setCategory(value.getCategory());
                actualLossStatisticsMesGroupVO.setInitDefectiveProductNum(value.getInitDefectiveProductNum());
                actualLossStatisticsMesGroupVO.setVirtualReporteQuantity(value.getVirtualReporteQuantity());
                actualLossStatisticsMesGroupVO.setDevelopmentReturns(value.getDevelopmentReturns());
                actualLossStatisticsMesGroupVO.setCustomerOfflineQuantity(value.getCustomerOfflineQuantity());
                actualLossStatisticsMesGroupVO.setInitReportingNum(value.getInitReportingNum());
                actualLossStatisticsMesGroupVO.setQualifiedQuantity(value.getQualifiedQuantity());
                actualLossStatisticsMesGroupVO.setBillingQuantity(value.getBillingQuantity());
                actualLossStatisticsMesGroupVO.setStandardLossQuantity(value.getStandardLossQuantity());
                actualLossStatisticsMesGroupVO.setRatedLossQuantity(value.getRatedLossQuantity());
//                actualLossStatisticsMesGroupVO.setBillingLossQuantity(value.getBillingLossQuantity());
                actualLossStatisticsMesGroupVO.setWorkOrderQuantity(value.getWorkOrderQuantity());
//                actualLossStatisticsMesGroupVO.setInitBillingLossQuantity(value.getInitBillingLossQuantity());
                actualLossStatisticsMesGroupVO.setInitStandardLossQuantity(value.getInitStandardLossQuantity());
                actualLossStatisticsMesGroupVO.setInitRatedLossQuantity(value.getInitRatedLossQuantity());
//                actualLossStatisticsMesGroupVO.setEndReportingNum(value.getEndReportingNum());
//                actualLossStatisticsMesGroupVO.setEndDefectiveProductNum(actualLossStatisticsMesGroupVO.getInitDefectiveProductNum().add(actualLossStatisticsMesGroupVO.getInitReportingNum()).subtract(actualLossStatisticsMesGroupVO.getEndReportingNum()));
                //实际损耗率
                if (actualLossStatisticsMesGroupVO.getQualifiedQuantity().compareTo(zero) != 0){
                    if ("纸板".equals(actualLossStatisticsMesGroupVO.getCategory())){
                        actualLossStatisticsMesGroupVO.setDefectiveProductNum(actualLossStatisticsMesGroupVO.getInitReportingNum().subtract(actualLossStatisticsMesGroupVO.getQualifiedQuantity()));
                    }else {
                        actualLossStatisticsMesGroupVO.setDefectiveProductNum(actualLossStatisticsMesGroupVO.getInitDefectiveProductNum().add(actualLossStatisticsMesGroupVO.getInitReportingNum()).subtract(actualLossStatisticsMesGroupVO.getQualifiedQuantity()));
                    }
                    if (actualLossStatisticsMesGroupVO.getDevelopmentReturns() != null ){
                        actualLossStatisticsMesGroupVO.setDefectiveProductNum(actualLossStatisticsMesGroupVO.getDefectiveProductNum().subtract(actualLossStatisticsMesGroupVO.getDevelopmentReturns()));
                    }
                    if (actualLossStatisticsMesGroupVO.getVirtualReporteQuantity() != null){
                        actualLossStatisticsMesGroupVO.setDefectiveProductNum(actualLossStatisticsMesGroupVO.getDefectiveProductNum().add(actualLossStatisticsMesGroupVO.getVirtualReporteQuantity()));
                    }
                    actualLossStatisticsMesGroupVO.setActualLossRate(actualLossStatisticsMesGroupVO.getDefectiveProductNum()
                            .divide(actualLossStatisticsMesGroupVO.getQualifiedQuantity(), 4, RoundingMode.HALF_UP)
                            .movePointRight(2));
                }
                //实际损耗率(末工序报工)
//                if (actualLossStatisticsMesGroupVO.getEndReportingNum().compareTo(zero) != 0){
//                    if ("纸板".equals(actualLossStatisticsMesGroupVO.getCategory())){
//                        actualLossStatisticsMesGroupVO.setEndActualLossRate(actualLossStatisticsMesGroupVO.getInitReportingNum().subtract(actualLossStatisticsMesGroupVO.getEndReportingNum())
//                                .divide(actualLossStatisticsMesGroupVO.getEndReportingNum(), 4, RoundingMode.HALF_UP)
//                                .movePointRight(2));
//                    }else {
//                        actualLossStatisticsMesGroupVO.setEndActualLossRate(actualLossStatisticsMesGroupVO.getInitDefectiveProductNum().add(actualLossStatisticsMesGroupVO.getInitReportingNum()).subtract(actualLossStatisticsMesGroupVO.getEndReportingNum())
//                                .divide(actualLossStatisticsMesGroupVO.getEndReportingNum(), 4, RoundingMode.HALF_UP)
//                                .movePointRight(2));
//                    }
//                }

                //纸板的理论损耗需要扣去首工序的损耗量
                if ("纸板".equals(actualLossStatisticsMesGroupVO.getCategory())){
                    //标准损耗率
                    actualLossStatisticsMesGroupVO.setStandardLossRate(actualLossStatisticsMesGroupVO.getStandardLossQuantity().subtract(actualLossStatisticsMesGroupVO.getInitStandardLossQuantity())
                            .divide(actualLossStatisticsMesGroupVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupVO.getStandardLossQuantity()),4,RoundingMode.HALF_UP)
                            .movePointRight(2));
                    //额定损耗率
                    actualLossStatisticsMesGroupVO.setRatedLossRate(actualLossStatisticsMesGroupVO.getRatedLossQuantity().subtract(actualLossStatisticsMesGroupVO.getInitRatedLossQuantity())
                            .divide(actualLossStatisticsMesGroupVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupVO.getRatedLossQuantity()),4,RoundingMode.HALF_UP)
                            .movePointRight(2));
                    //开单损耗率
//                    actualLossStatisticsMesGroupVO.setBillingLossRate(actualLossStatisticsMesGroupVO.getBillingLossQuantity().subtract(actualLossStatisticsMesGroupVO.getInitBillingLossQuantity())
//                            .divide(actualLossStatisticsMesGroupVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupVO.getBillingLossQuantity()),4,RoundingMode.HALF_UP)
//                            .movePointRight(2));
                }else {
                    //标准损耗率
                    actualLossStatisticsMesGroupVO.setStandardLossRate(actualLossStatisticsMesGroupVO.getStandardLossQuantity()
                            .divide(actualLossStatisticsMesGroupVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupVO.getStandardLossQuantity()),4,RoundingMode.HALF_UP)
                            .movePointRight(2));
                    //额定损耗率
                    actualLossStatisticsMesGroupVO.setRatedLossRate(actualLossStatisticsMesGroupVO.getRatedLossQuantity()
                            .divide(actualLossStatisticsMesGroupVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupVO.getRatedLossQuantity()),4,RoundingMode.HALF_UP)
                            .movePointRight(2));
                    //开单损耗率
//                    actualLossStatisticsMesGroupVO.setBillingLossRate(actualLossStatisticsMesGroupVO.getBillingLossQuantity()
//                            .divide(actualLossStatisticsMesGroupVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupVO.getBillingLossQuantity()),4,RoundingMode.HALF_UP)
//                            .movePointRight(2));
                }

                //产出比
                if (actualLossStatisticsMesGroupVO.getActualLossRate() != null && actualLossStatisticsMesGroupVO.getRatedLossRate() != null){
                    BigDecimal decVar = new BigDecimal("1").add(actualLossStatisticsMesGroupVO.getActualLossRate().movePointLeft(2));
                    BigDecimal rateVar = new BigDecimal("1").add(actualLossStatisticsMesGroupVO.getRatedLossRate().movePointLeft(2));
                    if (rateVar.compareTo(new BigDecimal(0))!=0){
                        actualLossStatisticsMesGroupVO.setOutputRatio(decVar
                                .divide(rateVar, 4, RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }
                }
                actualLossStatisticsMesGroupVOS.add(actualLossStatisticsMesGroupVO);
            }
            // 使用自定义比较器进行排序
            Collections.sort(actualLossStatisticsMesGroupVOS, new Comparator<ActualLossStatisticsMesGroupVO>() {
                @Override
                public int compare(ActualLossStatisticsMesGroupVO p1, ActualLossStatisticsMesGroupVO p2) {
                    // 首先按分类字段进行排序，如果分类名称相同再按工厂名称字段进行排序
                    int categoryComparison = p1.getCategory().compareTo(p2.getCategory());
                    if (categoryComparison == 0) {
                        return p1.getFactoryName().compareTo(p2.getFactoryName());
                    } else {
                        return categoryComparison;
                    }
                }
            });
            return actualLossStatisticsMesGroupVOS;
        }
        return null;
    }

    @Override
    public List<ActualLossStatisticsMesGroupDetailVO> selectActualLosssDetail(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        if (actualLossStatisticsParamGroupDTO == null){
            throw new CommonException("请求参数不能为空");
        }
        if (StringUtil.isEmpty(actualLossStatisticsParamGroupDTO.getStartDate())){
            throw new CommonException("开始日期不能为空");
        }else {
            if(!TimeTypeUtil.isLegalDate(actualLossStatisticsParamGroupDTO.getStartDate().length(), actualLossStatisticsParamGroupDTO.getStartDate(), "yyyy-MM-dd")){
                throw new CommonException("开始日期不符合格式：yyyy-MM-dd");
            }
        }
        if (StringUtil.isEmpty(actualLossStatisticsParamGroupDTO.getEndDate())){
            throw new CommonException("结束日期不能为空");
        }else {
            if(!TimeTypeUtil.isLegalDate(actualLossStatisticsParamGroupDTO.getEndDate().length(), actualLossStatisticsParamGroupDTO.getEndDate(), "yyyy-MM-dd")){
                throw new CommonException("结束日期不符合格式：yyyy-MM-dd");
            }
        }

        ActualLossStatisticsDTO lossStatisticsDTO = new ActualLossStatisticsDTO();
        if ("3".equals(actualLossStatisticsParamGroupDTO.getStatisticalCategory())){//半成品只显示安徽吉宏
            lossStatisticsDTO.setFactoryCode(SiteEnum.AHGC.getCode());
        }else {
            lossStatisticsDTO.setFactoryCode(actualLossStatisticsParamGroupDTO.getFactoryCode());
        }
        lossStatisticsDTO.setStatisticalCategory(actualLossStatisticsParamGroupDTO.getStatisticalCategory());
        Date startDate = DateUtil.parse(actualLossStatisticsParamGroupDTO.getStartDate(),"yyyy-MM-dd");
        Date endDate = DateUtil.offsetDay(DateUtil.parse(actualLossStatisticsParamGroupDTO.getEndDate(),"yyyy-MM-dd"),1);
        lossStatisticsDTO.setStartDate(DateUtil.format(startDate,"yyyy-MM-dd"));
        lossStatisticsDTO.setEndDate(DateUtil.format(endDate,"yyyy-MM-dd"));
        lossStatisticsDTO.setProductCategoryNo(actualLossStatisticsParamGroupDTO.getProductCategoryNo());
        lossStatisticsDTO.setFactoryType(actualLossStatisticsParamGroupDTO.getFactoryType());
        lossStatisticsDTO.setProcessNo(actualLossStatisticsParamGroupDTO.getProcessNo());

        List<ActualLossStatisticsGroupVO> actualLossStatisticsVOS = sffbTService.selectActualLosssGroupSheetDetail(lossStatisticsDTO);
        List<ReturnMaterialVO> returnMaterialVOS = sfcbTService.getReturnMaterialSheet(lossStatisticsDTO.getStartDate(),lossStatisticsDTO.getEndDate());
        if (CollectionUtil.isNotEmpty(actualLossStatisticsVOS)){
            //计算损耗率
            BigDecimal zero = new BigDecimal("0");
            Map<String,ActualLossStatisticsGroupVO> actualLossStatisticsGroupVOMap = new HashMap<>();
            //根据据点、工单号、工序编码为唯一值，保存为map对象
            for (ActualLossStatisticsGroupVO actualLossStatisticsVO : actualLossStatisticsVOS) {
                actualLossStatisticsGroupVOMap.put(actualLossStatisticsVO.getFactoryNo()+actualLossStatisticsVO.getWorkOrderNO()+actualLossStatisticsVO.getProcessNo(),actualLossStatisticsVO);
            }
            List<ActualLossStatisticsGroupVO> actualLossStatisticsGroupVOS = new ArrayList<>();
            Map<String,ActualLossStatisticsGroupVO> actualLossStatisticsGroupVOHslcMap = new HashMap<>();
            for (ActualLossStatisticsGroupVO actualLossStatisticsGroupVO:actualLossStatisticsVOS){

                if ("INIT".equals(actualLossStatisticsGroupVO.getLastProcess()) && !"SITE-061".equals(actualLossStatisticsGroupVO.getFactoryNo())){
                    ActualLossStatisticsGroupVO actualLossStatisticsGroupVONew = new ActualLossStatisticsGroupVO();
                    actualLossStatisticsGroupVONew.setFactoryNo(actualLossStatisticsGroupVO.getFactoryNo());
                    actualLossStatisticsGroupVONew.setFactoryName(actualLossStatisticsGroupVO.getFactoryName());
                    actualLossStatisticsGroupVONew.setCustomerNo(actualLossStatisticsGroupVO.getCustomerNo());
                    actualLossStatisticsGroupVONew.setCustomerName(actualLossStatisticsGroupVO.getCustomerName());
                    actualLossStatisticsGroupVONew.setProductNo(actualLossStatisticsGroupVO.getProductNo());
                    actualLossStatisticsGroupVONew.setProductName(actualLossStatisticsGroupVO.getProductName());
                    actualLossStatisticsGroupVONew.setCategory(actualLossStatisticsGroupVO.getCategory());
                    actualLossStatisticsGroupVONew.setPiece(actualLossStatisticsGroupVO.getPiece());
                    actualLossStatisticsGroupVONew.setProcessNo(actualLossStatisticsGroupVO.getProcessNo());
                    actualLossStatisticsGroupVONew.setProcessName(actualLossStatisticsGroupVO.getProcessName());
                    actualLossStatisticsGroupVONew.setLossType(actualLossStatisticsGroupVO.getLossType());
                    actualLossStatisticsGroupVONew.setWorkOrderNO(actualLossStatisticsGroupVO.getWorkOrderNO());
                    actualLossStatisticsGroupVONew.setLastProcess(actualLossStatisticsGroupVO.getLastProcess());
                    actualLossStatisticsGroupVONew.setNextProcess(actualLossStatisticsGroupVO.getNextProcess());
                    actualLossStatisticsGroupVONew.setInitDefectiveProductNum(actualLossStatisticsGroupVO.getInitDefectiveProductNum());
                    actualLossStatisticsGroupVONew.setQualifiedQuantity(actualLossStatisticsGroupVO.getQualifiedQuantity());
                    actualLossStatisticsGroupVONew.setWorkOrderQuantity(actualLossStatisticsGroupVO.getWorkOrderQuantity());
                    actualLossStatisticsGroupVONew.setCustomerOfflineQuantity(actualLossStatisticsGroupVO.getCustomerOfflineQuantity());
                    actualLossStatisticsGroupVONew.setVirtualReporteQuantity(actualLossStatisticsGroupVO.getVirtualReporteQuantity());
                    actualLossStatisticsGroupVONew.setInitReportingNum(actualLossStatisticsGroupVO.getInitReportingNum());
                    actualLossStatisticsGroupVONew.setGoodProductQuantity(actualLossStatisticsGroupVO.getGoodProductQuantity());
                    actualLossStatisticsGroupVONew.setBillingQuantity(actualLossStatisticsGroupVO.getBillingQuantity());
                    actualLossStatisticsGroupVONew.setInitBillingLossQuantity(actualLossStatisticsGroupVO.getInitBillingLossQuantity());
                    actualLossStatisticsGroupVONew.setInitStandardLossQuantity(actualLossStatisticsGroupVO.getInitStandardLossQuantity());
                    actualLossStatisticsGroupVONew.setInitRatedLossQuantity(actualLossStatisticsGroupVO.getInitRatedLossQuantity());
                    String nextProcess = actualLossStatisticsGroupVO.getNextProcess();
                    BigDecimal standardLossQuantity = actualLossStatisticsGroupVO.getStandardLossQuantity();
                    BigDecimal ratedLossQuantity = actualLossStatisticsGroupVO.getRatedLossQuantity();
                    BigDecimal billingLossQuantity = actualLossStatisticsGroupVO.getBillingLossQuantity();
                    BigDecimal endReportingNum = actualLossStatisticsGroupVO.getEndReportingNum();
                    //假如是纸板的理论，抄瓦首工序理论损耗置为0，实际不良置为0
//                    if("纸板".equals(actualLossStatisticsGroupVO.getCategory())){
//                        actualLossStatisticsGroupVONew.setInitDefectiveProductNum(new BigDecimal(0));
//                        standardLossQuantity = new BigDecimal(0);
//                        ratedLossQuantity = new BigDecimal(0);
//                        billingLossQuantity = new BigDecimal(0);
//                    }
                    while (nextProcess != null && !nextProcess.equals("") && !nextProcess.equals("END")){
                        ActualLossStatisticsGroupVO voMap =actualLossStatisticsGroupVOMap.get(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getWorkOrderNO()+nextProcess);
                        log.debug("voMap is : "+voMap+"and standardLossQuantity is :"+standardLossQuantity);
                        if (voMap != null){
                            standardLossQuantity = (standardLossQuantity==null?new BigDecimal(0):standardLossQuantity).add(voMap.getStandardLossQuantity()==null?new BigDecimal(0):voMap.getStandardLossQuantity());
                            ratedLossQuantity = (ratedLossQuantity==null?new BigDecimal(0):ratedLossQuantity).add(voMap.getRatedLossQuantity()==null?new BigDecimal(0):voMap.getRatedLossQuantity());
                            billingLossQuantity = (billingLossQuantity==null?new BigDecimal(0):billingLossQuantity).add(voMap.getBillingLossQuantity()==null?new BigDecimal(0):voMap.getBillingLossQuantity());
                            endReportingNum = (endReportingNum==null?new BigDecimal(0):endReportingNum).add(voMap.getEndReportingNum()==null?new BigDecimal(0):voMap.getEndReportingNum());
                            nextProcess = voMap.getNextProcess();
                        }else {
                            nextProcess = "END";
                        }
                    }
                    actualLossStatisticsGroupVONew.setStandardLossQuantity(standardLossQuantity);
                    actualLossStatisticsGroupVONew.setRatedLossQuantity(ratedLossQuantity);
                    actualLossStatisticsGroupVONew.setBillingLossQuantity(billingLossQuantity);
                    actualLossStatisticsGroupVONew.setEndReportingNum(endReportingNum);
                    actualLossStatisticsGroupVOS.add(actualLossStatisticsGroupVONew);
                }
                if ("INIT".equals(actualLossStatisticsGroupVO.getLastProcess()) && "SITE-061".equals(actualLossStatisticsGroupVO.getFactoryNo())){
                    if (actualLossStatisticsGroupVO.getRn()==1){
                        ActualLossStatisticsGroupVO actualLossStatisticsGroupVONew = new ActualLossStatisticsGroupVO();
                        actualLossStatisticsGroupVONew.setFactoryNo(actualLossStatisticsGroupVO.getFactoryNo());
                        actualLossStatisticsGroupVONew.setFactoryName(actualLossStatisticsGroupVO.getFactoryName());
                        actualLossStatisticsGroupVONew.setCustomerNo(actualLossStatisticsGroupVO.getCustomerNo());
                        actualLossStatisticsGroupVONew.setCustomerName(actualLossStatisticsGroupVO.getCustomerName());
                        actualLossStatisticsGroupVONew.setProductNo(actualLossStatisticsGroupVO.getProductNo());
                        actualLossStatisticsGroupVONew.setProductName(actualLossStatisticsGroupVO.getProductName());
                        actualLossStatisticsGroupVONew.setCategory(actualLossStatisticsGroupVO.getCategory());
                        actualLossStatisticsGroupVONew.setPiece(actualLossStatisticsGroupVO.getPiece());
                        actualLossStatisticsGroupVONew.setProcessNo(actualLossStatisticsGroupVO.getProcessNo());
                        actualLossStatisticsGroupVONew.setProcessName(actualLossStatisticsGroupVO.getProcessName());
                        actualLossStatisticsGroupVONew.setLossType(actualLossStatisticsGroupVO.getLossType());
                        actualLossStatisticsGroupVONew.setWorkOrderNO(actualLossStatisticsGroupVO.getWorkOrderNO());
                        actualLossStatisticsGroupVONew.setLastProcess(actualLossStatisticsGroupVO.getLastProcess());
                        actualLossStatisticsGroupVONew.setNextProcess(actualLossStatisticsGroupVO.getNextProcess());
                        actualLossStatisticsGroupVONew.setInitDefectiveProductNum(actualLossStatisticsGroupVO.getInitDefectiveProductNum());
                        actualLossStatisticsGroupVONew.setQualifiedQuantity(actualLossStatisticsGroupVO.getQualifiedQuantity());
                        actualLossStatisticsGroupVONew.setWorkOrderQuantity(actualLossStatisticsGroupVO.getWorkOrderQuantity());
                        actualLossStatisticsGroupVONew.setCustomerOfflineQuantity(actualLossStatisticsGroupVO.getCustomerOfflineQuantity());
                        actualLossStatisticsGroupVONew.setVirtualReporteQuantity(actualLossStatisticsGroupVO.getVirtualReporteQuantity());
                        actualLossStatisticsGroupVONew.setInitReportingNum(actualLossStatisticsGroupVO.getInitReportingNum());
                        actualLossStatisticsGroupVONew.setGoodProductQuantity(actualLossStatisticsGroupVO.getGoodProductQuantity());
                        actualLossStatisticsGroupVONew.setBillingQuantity(actualLossStatisticsGroupVO.getBillingQuantity());
                        actualLossStatisticsGroupVONew.setInitBillingLossQuantity(actualLossStatisticsGroupVO.getInitBillingLossQuantity());
                        actualLossStatisticsGroupVONew.setInitStandardLossQuantity(actualLossStatisticsGroupVO.getInitStandardLossQuantity());
                        actualLossStatisticsGroupVONew.setInitRatedLossQuantity(actualLossStatisticsGroupVO.getInitRatedLossQuantity());
                        String nextProcess = actualLossStatisticsGroupVO.getNextProcess();
                        BigDecimal standardLossQuantity = actualLossStatisticsGroupVO.getStandardLossQuantity();
                        BigDecimal ratedLossQuantity = actualLossStatisticsGroupVO.getRatedLossQuantity();
                        BigDecimal billingLossQuantity = actualLossStatisticsGroupVO.getBillingLossQuantity();
                        BigDecimal endReportingNum = actualLossStatisticsGroupVO.getEndReportingNum();
                        //假如是纸板的理论，抄瓦首工序理论损耗置为0，实际不良置为0
//                    if("纸板".equals(actualLossStatisticsGroupVO.getCategory())){
//                        actualLossStatisticsGroupVONew.setInitDefectiveProductNum(new BigDecimal(0));
//                        standardLossQuantity = new BigDecimal(0);
//                        ratedLossQuantity = new BigDecimal(0);
//                        billingLossQuantity = new BigDecimal(0);
//                    }
                        while (nextProcess != null && !nextProcess.equals("") && !nextProcess.equals("END")){
                            ActualLossStatisticsGroupVO voMap =actualLossStatisticsGroupVOMap.get(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getWorkOrderNO()+nextProcess);
                            log.debug("voMap is : "+voMap+"and standardLossQuantity is :"+standardLossQuantity);
                            if (voMap != null){
                                standardLossQuantity = (standardLossQuantity==null?new BigDecimal(0):standardLossQuantity).add(voMap.getStandardLossQuantity()==null?new BigDecimal(0):voMap.getStandardLossQuantity());
                                ratedLossQuantity = (ratedLossQuantity==null?new BigDecimal(0):ratedLossQuantity).add(voMap.getRatedLossQuantity()==null?new BigDecimal(0):voMap.getRatedLossQuantity());
                                billingLossQuantity = (billingLossQuantity==null?new BigDecimal(0):billingLossQuantity).add(voMap.getBillingLossQuantity()==null?new BigDecimal(0):voMap.getBillingLossQuantity());
                                endReportingNum = (endReportingNum==null?new BigDecimal(0):endReportingNum).add(voMap.getEndReportingNum()==null?new BigDecimal(0):voMap.getEndReportingNum());
                                nextProcess = voMap.getNextProcess();
                            }else {
                                nextProcess = "END";
                            }
                        }
                        actualLossStatisticsGroupVONew.setStandardLossQuantity(standardLossQuantity);
                        actualLossStatisticsGroupVONew.setRatedLossQuantity(ratedLossQuantity);
                        actualLossStatisticsGroupVONew.setBillingLossQuantity(billingLossQuantity);
                        actualLossStatisticsGroupVONew.setEndReportingNum(endReportingNum);
                        actualLossStatisticsGroupVOHslcMap.put(actualLossStatisticsGroupVO.getFactoryNo()+actualLossStatisticsGroupVO.getWorkOrderNO()+actualLossStatisticsGroupVO.getCategory(),actualLossStatisticsGroupVONew);
                    }
                }
            }
            actualLossStatisticsGroupVOS.addAll(actualLossStatisticsGroupVOHslcMap.values());
            List<ActualLossStatisticsMesGroupDetailVO> actualLossStatisticsMesGroupDetailVOS = BeanUtil.copyToList(actualLossStatisticsGroupVOS,ActualLossStatisticsMesGroupDetailVO.class);
            //退料map
            Map<String,ReturnMaterialVO> returnMaterialVOMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(returnMaterialVOS)){
                for (ReturnMaterialVO returnMaterialVO:returnMaterialVOS){
                    returnMaterialVOMap.put(returnMaterialVO.getFactoryNo()+returnMaterialVO.getWorkOrderNO()+returnMaterialVO.getProcessNo()+returnMaterialVO.getCategory(),returnMaterialVO);
                }
            }
            for (ActualLossStatisticsMesGroupDetailVO actualLossStatisticsMesGroupDetailVO:actualLossStatisticsMesGroupDetailVOS){
                actualLossStatisticsMesGroupDetailVO.setInitTheoryOutPut(actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getInitBillingLossQuantity()));
                actualLossStatisticsMesGroupDetailVO.setDefectiveProductNum(actualLossStatisticsMesGroupDetailVO.getInitDefectiveProductNum().add(actualLossStatisticsMesGroupDetailVO.getInitReportingNum()).subtract(actualLossStatisticsMesGroupDetailVO.getQualifiedQuantity()));
//                actualLossStatisticsMesGroupDetailVO.setEndDefectiveProductNum(actualLossStatisticsMesGroupDetailVO.getInitDefectiveProductNum().add(actualLossStatisticsMesGroupDetailVO.getInitReportingNum()).subtract(actualLossStatisticsMesGroupDetailVO.getEndReportingNum()));
                actualLossStatisticsMesGroupDetailVO.setUnderCountQuantity(actualLossStatisticsMesGroupDetailVO.getQualifiedQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getWorkOrderQuantity()));
                //退料数量
                ReturnMaterialVO returnMaterialVO = returnMaterialVOMap.get(actualLossStatisticsMesGroupDetailVO.getFactoryNo()+actualLossStatisticsMesGroupDetailVO.getWorkOrderNO()+actualLossStatisticsMesGroupDetailVO.getProcessNo()+actualLossStatisticsMesGroupDetailVO.getCategory());
                if (returnMaterialVO != null){
                    actualLossStatisticsMesGroupDetailVO.setReturnQuantity(returnMaterialVO.getReturnQuantity().multiply(BigDecimal.ONE.negate()));
                }
                if (actualLossStatisticsMesGroupDetailVO.getQualifiedQuantity().compareTo(zero) != 0){
                    BigDecimal lossQuantity = new BigDecimal("0");
                    lossQuantity = actualLossStatisticsMesGroupDetailVO.getInitDefectiveProductNum().add(actualLossStatisticsMesGroupDetailVO.getInitReportingNum());
                    if (actualLossStatisticsMesGroupDetailVO.getReturnQuantity() != null){
                        lossQuantity = lossQuantity.add(actualLossStatisticsMesGroupDetailVO.getReturnQuantity());
                    }
                    if (actualLossStatisticsMesGroupDetailVO.getVirtualReporteQuantity() != null){
                        lossQuantity = lossQuantity.add(actualLossStatisticsMesGroupDetailVO.getVirtualReporteQuantity());
                    }
                    actualLossStatisticsMesGroupDetailVO.setActualLossRate(lossQuantity.subtract(actualLossStatisticsMesGroupDetailVO.getQualifiedQuantity())
                            .divide(actualLossStatisticsMesGroupDetailVO.getQualifiedQuantity(), 4, RoundingMode.HALF_UP)
                            .movePointRight(2));
                }
//                if (actualLossStatisticsMesGroupDetailVO.getEndReportingNum().compareTo(zero) != 0){
//                    BigDecimal lossQuantity = new BigDecimal("0");
//                    lossQuantity = actualLossStatisticsMesGroupDetailVO.getInitDefectiveProductNum().add(actualLossStatisticsMesGroupDetailVO.getInitReportingNum());
//                    if (actualLossStatisticsMesGroupDetailVO.getReturnQuantity() != null){
//                        lossQuantity = lossQuantity.add(actualLossStatisticsMesGroupDetailVO.getReturnQuantity());
//                    }
//                    if (actualLossStatisticsMesGroupDetailVO.getVirtualReporteQuantity() != null){
//                        lossQuantity = lossQuantity.add(actualLossStatisticsMesGroupDetailVO.getVirtualReporteQuantity());
//                    }
//                    actualLossStatisticsMesGroupDetailVO.setEndActualLossRate(lossQuantity.subtract(actualLossStatisticsMesGroupDetailVO.getEndReportingNum())
//                            .divide(actualLossStatisticsMesGroupDetailVO.getEndReportingNum(), 4, RoundingMode.HALF_UP)
//                            .movePointRight(2));
//                }
                //纸板的理论损耗需要扣去首工序的损耗量
                if ("纸板".equals(actualLossStatisticsMesGroupDetailVO.getCategory())){
                    //标准损耗率
                    if (actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getStandardLossQuantity()).compareTo(zero) != 0){
                        actualLossStatisticsMesGroupDetailVO.setStandardLossRate(actualLossStatisticsMesGroupDetailVO.getStandardLossQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getInitStandardLossQuantity())
                                .divide(actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getStandardLossQuantity()),4,RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }

                    //额定损耗率
                    if (actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getRatedLossQuantity()).compareTo(zero) != 0){
                        actualLossStatisticsMesGroupDetailVO.setRatedLossRate(actualLossStatisticsMesGroupDetailVO.getRatedLossQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getInitRatedLossQuantity())
                                .divide(actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getRatedLossQuantity()),4,RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }
                    //开单损耗率
                    if (actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getBillingLossQuantity()).compareTo(zero) != 0){
                        actualLossStatisticsMesGroupDetailVO.setBillingLossRate(actualLossStatisticsMesGroupDetailVO.getBillingLossQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getInitBillingLossQuantity())
                                .divide(actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getBillingLossQuantity()),4,RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }
                }else {
                    //标准损耗率
                    actualLossStatisticsMesGroupDetailVO.setStandardLossRate(actualLossStatisticsMesGroupDetailVO.getStandardLossQuantity()
                            .divide(actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getStandardLossQuantity()),4,RoundingMode.HALF_UP)
                            .movePointRight(2));
                    //额定损耗率
                    actualLossStatisticsMesGroupDetailVO.setRatedLossRate(actualLossStatisticsMesGroupDetailVO.getRatedLossQuantity()
                            .divide(actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getRatedLossQuantity()),4,RoundingMode.HALF_UP)
                            .movePointRight(2));
                    //开单损耗率
                    actualLossStatisticsMesGroupDetailVO.setBillingLossRate(actualLossStatisticsMesGroupDetailVO.getBillingLossQuantity()
                            .divide(actualLossStatisticsMesGroupDetailVO.getBillingQuantity().subtract(actualLossStatisticsMesGroupDetailVO.getBillingLossQuantity()),4,RoundingMode.HALF_UP)
                            .movePointRight(2));
                }
            }
            // 使用自定义比较器进行排序
            Collections.sort(actualLossStatisticsMesGroupDetailVOS, new Comparator<ActualLossStatisticsMesGroupDetailVO>() {
                @Override
                public int compare(ActualLossStatisticsMesGroupDetailVO p1, ActualLossStatisticsMesGroupDetailVO p2) {
                    // 首先按分类字段进行排序，如果分类名称相同再按工厂名称字段进行排序
                    int categoryComparison = p1.getCategory().compareTo(p2.getCategory());
                    if (categoryComparison == 0) {
                        return p1.getFactoryName().compareTo(p2.getFactoryName());
                    } else {
                        return categoryComparison;
                    }
                }
            });
            return actualLossStatisticsMesGroupDetailVOS;
        }
        return null;
    }

    @Override
    public String exportToExcel(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        List<ActualLossStatisticsMesGroupVO> actualLossStatisticsMesGroupVOS = selectActualLosss(actualLossStatisticsParamGroupDTO);
        String url = getDataToExcel(actualLossStatisticsMesGroupVOS,ActualLossStatisticsMesGroupVO.class,"集团总损耗表");
        return url;
    }

    @Override
    public String exportToExcelDetail(ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO) {
        List<ActualLossStatisticsMesGroupDetailVO> actualLossStatisticsMesGroupVOS = selectActualLosssDetail(actualLossStatisticsParamGroupDTO);
        String url = getDataToExcel(actualLossStatisticsMesGroupVOS,ActualLossStatisticsMesGroupDetailVO.class,"集团总损耗表明细");
        return url;
    }

    private String getDataToExcel(Collection<?> data,
                                  Class<?> clz, String fileName) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        EasyExcel.write(os, clz).sheet("模板").doWrite(() -> {
            // 分页查询数据
            return data;
        });

        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(os.toByteArray());
            String OBJECT_NAME = "temp";
            String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
            String objectName = OBJECT_NAME + "/" + dateStr + "/" + System.currentTimeMillis()
                    + "/" + fileName + ".xlsx";
            String uploadFile = OssUtils.uploadFile(aliyunOssProperty.getAccessId(), aliyunOssProperty.getAccessKey(),
                    aliyunOssProperty.getHzEndpoint(), aliyunOssProperty.getCommissionBucketName(), objectName,
                    byteArrayInputStream);
            return uploadFile;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 关闭
            try {
                byteArrayInputStream.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
            try {
                os.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }
}
