package cn.jihong.mes.app.controller;

import java.util.List;

import javax.annotation.Resource;

import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.ProcessLossParamGroupDTO;
import cn.jihong.mes.api.model.vo.ProcessLossMesGroupVO;
import cn.jihong.mes.api.service.IProcessLossGroupService;

/**
 * 工序损耗率(集团)
 */
@RestController
@RequestMapping("/processLossGroup")
@ShenyuSpringMvcClient(path = "/processLossGroup/**")
public class ProcessLossGroupController {
    @Resource
    private IProcessLossGroupService processLossGroupService;

    /**
     * 查询工序损耗率
     * @param processLossParamGroupDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ProcessLossMesGroupVO>> getList(@RequestBody ProcessLossParamGroupDTO processLossParamGroupDTO) {
        List<ProcessLossMesGroupVO>  processLossMesGroupVOS = processLossGroupService.selectProcessLoss(processLossParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, processLossMesGroupVOS);
    }

    /**
     * 导出excel
     * @param processLossParamGroupDTO
     * @return
     */
    @PostMapping("exportToExcel")
    public StandardResult<String> exportToExcel(@RequestBody ProcessLossParamGroupDTO processLossParamGroupDTO) {
        String url = processLossGroupService.exportToExcel(processLossParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }

    /**
     * 导出明细excel
     * @param processLossParamGroupDTO
     * @return
     */
    @PostMapping("exportToExcelDetail")
    public StandardResult<String> exportToExcelDetail(@RequestBody ProcessLossParamGroupDTO processLossParamGroupDTO) {
        String url = processLossGroupService.exportToExcelDetail(processLossParamGroupDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, url);
    }
}
