package cn.jihong.mes.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.dto.DailyProductionPlanAchievementRateDTO;
import cn.jihong.mes.api.model.dto.ProcessLossParamDTO;
import cn.jihong.mes.api.model.vo.DailyProductionPlanAchievementRateVO;
import cn.jihong.mes.api.model.vo.ProcessLossMesVO;
import cn.jihong.mes.api.service.IProcessLossService;
import cn.jihong.oa.erp.api.model.vo.ProcessLossVO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工序损耗率
 */
@RestController
@RequestMapping("/processLoss")
@ShenyuSpringMvcClient(path = "/processLoss/**")
public class ProcessLossController {
    @Resource
    private IProcessLossService processLossService;

    /**
     * 查询工序损耗率
     * @param processLossParamDTO
     * @return
     */
    @PostMapping("list")
    public StandardResult<List<ProcessLossMesVO>> getList(@RequestBody ProcessLossParamDTO processLossParamDTO) {
        List<ProcessLossMesVO>  processLossMesVOS = processLossService.selectProcessLoss(processLossParamDTO);
        return StandardResult.resultCode(OperateCode.SUCCESS, processLossMesVOS);
    }
}
