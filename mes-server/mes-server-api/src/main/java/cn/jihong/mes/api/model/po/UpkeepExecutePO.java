package cn.jihong.mes.api.model.po;

import cn.jihong.mes.api.model.constant.TbBaseConstant;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备保养执行
 * <AUTHOR>
 * @date 2023/9/1 15:01
 */
@Data
@TableName("upkeep_execute")
public class UpkeepExecutePO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @TableId(value = TbBaseConstant.ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工序
     */
    @TableField("production_process")
    private String productionProcess;



    /**
     * 生产机台
     */
    @TableField("production_machine")
    private String productionMachine;

    /**
     * 生产计划开始时间
     */
    @TableField("production_plan_start_time")
    private Date productionPlanStartTime;

    /**
     *  保养用时
     */
    @TableField("upkeep_time")
    private Double upkeepTime;

    /**
     *  保养类型
     */
    @TableField("upkeep_type")
    private String upkeepType;


    /**
     * 是否派发
     */
    @TableField("distribute")
    private Integer distribute;


    /**
     * 创建人
     */
    @TableField(TbBaseConstant.CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = TbBaseConstant.CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(TbBaseConstant.UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = TbBaseConstant.UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(TbBaseConstant.IS_DELETE)
    private Integer isDelete;

    /**
     * 生产计划日期
     */
    @TableField(exist = false)
    private Date productionPlanDate;

}
