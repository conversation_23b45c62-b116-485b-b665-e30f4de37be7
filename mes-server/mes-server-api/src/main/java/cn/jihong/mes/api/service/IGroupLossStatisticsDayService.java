package cn.jihong.mes.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.api.model.dto.GroupLossStatisticsDayDTO;
import cn.jihong.mes.api.model.dto.GroupLossStatisticsDayParamDTO;
import cn.jihong.mes.api.model.po.GroupLossStatisticsDayPO;
import cn.jihong.mes.api.model.vo.GroupLossStatisticsDayVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 * 集团损耗统计日报-单位张 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
public interface IGroupLossStatisticsDayService extends IJiHongService<GroupLossStatisticsDayPO> {
    void saveBatch(String startDate,String endDate);
    Pagination<GroupLossStatisticsDayVO> getList(GroupLossStatisticsDayParamDTO groupLossStatisticsDayParamDTO);
}
