package cn.jihong.mes.api.model.dto;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <p>
 * 生产计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
public class ProductionPlanDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 生产计划日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productionPlanDate;

    /**
     * 生产计划开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm")
    @DateTimeFormat(pattern = "HH:mm")
    private Date productionPlanStartTime;

    /**
     * 生产计划结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm")
    @DateTimeFormat(pattern = "HH:mm")
    private Date productionPlanEndTime;


    /**
     * 生产部门编号
     */
    private String deptNo;

    /**
     * 生产部门名称
     */
    private String deptName;

    /**
     * 产品名称
     */
    private String productionName;


    /**
     * 工序
     */
    private String productionProcess;

    /**
     * 生产机台编号
     */
    private String productionMachineNo;

    /**
     * 生产机台
     */
    private String productionMachine;


    /**
     * 工单号
     */
    private String workerOrderNo;


    /**
     * 单位
     */
    private String unit;


    /**
     * 标准产能/H
     */
    private Double standardProductionCapacity;


    /**
     * 换产时长
     */
    private Double productionChangeHours;


    /**
     * 计划产量
     */
    private Double plannedProductionCapacity;


    /**
     * 星期几
     */
    private String weekDay;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 编辑人
     */
    private Long updateBy;


    /**
     * 编辑时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 公司id
     */
    private Integer companyId;

    /**
     * 班次顺序
     */
    private Integer serialNo;


    /**
     * 保养类型
     */
    private String upkeepType;

    /**
     * 保养时间
     */
    private Double upkeepTime;

    /**
     * erp机台名称
     */
    private String erpMachineName;

    /**
     * 计划类型：1-换产，2-生产，3-结单，4-重工
     */
    private String planType;

    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 生产批次
     */
    private String productionBatch;

    /**
     * 备注
     */
    private String remark;

}
