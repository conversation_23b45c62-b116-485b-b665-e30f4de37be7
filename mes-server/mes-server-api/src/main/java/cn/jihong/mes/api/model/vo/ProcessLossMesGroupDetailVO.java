package cn.jihong.mes.api.model.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class ProcessLossMesGroupDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司编号
     */
    @ExcelProperty("据点")
    private String factoryCode;

    /**
     * 公司名称
     */
    @ExcelProperty("公司名称")
    private String factoryName;

    /**
     * 客户编号
     */
    @ExcelProperty("客户编号")
    private String customerNo;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 产品编号
     */
    @ExcelProperty("产品编号")
    private String productNo;

    /**
     * 产品名称
     */
    @ExcelProperty("产品名称")
    private String productName;

    /**
     * 工序编号
     */
    @ExcelIgnore
    private String processNo;

    /**
     * 工序名称
     */
    @ExcelProperty("工序")
    private String processName;

    /**
     * 工序类型
     */
    @ExcelProperty("工序类型")
    private String processType;

    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;


    /**
     * 拼数
     */
    @ExcelProperty("拼数")
    private Integer piece;

    /**
     * 截断长
     */
    @ExcelProperty("截断长")
    private BigDecimal truncationLength;

    /**
     * 工单号
     */
    @ExcelProperty("工单号")
    private String workerOrderNo;

    /**
     * 投料数量
     */
    @ExcelProperty("实际投料量")
    private BigDecimal inputQuantity;

    /**
     * 不良品总数
     */
    @ExcelProperty("实际损耗量")
    private BigDecimal defectiveProductNum;

    /**
     * 已入库合格数量
     */
    @ExcelProperty("实际成品量")
    private BigDecimal qualifiedQuantity;

    /**
     * 实际损耗率
     */
    @ExcelProperty("实际损耗率")
    private BigDecimal actualLossRate;

    /**
     * 开单损耗率
     */
    @ExcelProperty("额定损耗率")
    private BigDecimal billingLossRate;

    /**
     * 其它工序报本工序不良数量
     */
    @ExcelProperty("其它工序报本工序不良数量")
    private BigDecimal otherDefectiveProductNum;

    /**
     * 本工序报其它工序不良数量
     */
    @ExcelProperty("本工序报其它工序不良数量")
    private BigDecimal currentDefectiveNum;

    /**
     * 本工序不良品报工数量
     */
    @ExcelProperty("本工序不良品报工数量")
    private BigDecimal defectiveProductReportNum;

    /**
     *工单数量
     */
    @ExcelIgnore
    private BigDecimal workOrderQuantity;

    /**
     * 开单损耗量
     */
    @ExcelIgnore
    private BigDecimal billingLossQuantity;

    /**
     * 开单投料数量
     */
    @ExcelIgnore
    private BigDecimal billingQuantity;

    /**
     * 开单成品数量
     */
    @ExcelProperty("工单成品数量")
    private BigDecimal billingQualifiedQuantity;

    /**
     * 是否首道工序
     */
    @ExcelProperty("是否首道工序")
    private String isInitProcess;

    /**
     * 客户下线数量
     */
    @ExcelProperty("客户下线数量")
    private BigDecimal customerOfflineQuantity;



}
