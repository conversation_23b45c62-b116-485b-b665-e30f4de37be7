package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.ProductionShiftDetailDTO;
import cn.jihong.mes.api.model.po.ProductionShiftDetailPO;
import cn.jihong.mes.api.model.vo.ProductionShiftDetailVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 * 班次详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface IProductionShiftDetailService extends IJiHongService<ProductionShiftDetailPO> {
    List<ProductionShiftDetailVO> getList(ProductionShiftDetailDTO productionShiftDetailDTO);
    void save(ProductionShiftDetailDTO productionShiftDetailDTO);
    void update(ProductionShiftDetailDTO productionShiftDetailDTO);
    void delete(String id);
    List<ProductionShiftDetailVO> getDetailList(String mainId);

    List<ProductionShiftDetailPO> getShiftsByCompanyCode(String companyCode);
}
