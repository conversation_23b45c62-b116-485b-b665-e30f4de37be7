package cn.jihong.mes.api.service;

import cn.jihong.mes.api.model.dto.TblCusProcessBarCodeBoxDTO;
import cn.jihong.mes.api.model.po.TblCusProcessBarCodeBoxPO;
import cn.jihong.mes.api.model.vo.TblCusProcessBarCodeBoxVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface ITblCusProcessBarCodeBoxService extends IJiHongService<TblCusProcessBarCodeBoxPO> {

    List<TblCusProcessBarCodeBoxVO> getTblCusProcessBarCodeBoxList(TblCusProcessBarCodeBoxDTO tblCusProcessBarCodeBoxDTO);
}
