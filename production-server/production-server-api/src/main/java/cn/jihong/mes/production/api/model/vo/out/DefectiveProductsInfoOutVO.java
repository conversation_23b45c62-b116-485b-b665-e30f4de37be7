package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class DefectiveProductsInfoOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 不良原因
     */
    private String defectiveProductsReason;
    private String defectiveProductsReasonName;

    /**
     * 不良数量
     */
    private BigDecimal defectiveProductsQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 不良来源
     */
    private String defectiveProductsSource;
    private String defectiveProductsSourceName;

    /**
     * 栈板码
     */
    private String palletCode;



    private Long createBy;

    private String createrName;

    /**
     * 班组人员id
     */
    private String teamUsers;
    private String teamUsersName;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次  1 白班  2 夜班
     */
    private Integer shift;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 出站时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outboundTime;

    /**
     * 不良类别：  0  白片  1 彩片
     */
    private Integer defectiveType;
    private String defectiveTypeName;

}
