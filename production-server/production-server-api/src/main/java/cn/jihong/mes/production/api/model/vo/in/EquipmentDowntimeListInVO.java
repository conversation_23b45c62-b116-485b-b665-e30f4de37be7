package cn.jihong.mes.production.api.model.vo.in;


import java.io.Serializable;
import java.time.LocalDateTime;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Getter;
import lombok.Setter;


/**
 * 设备停机代码表
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
public class EquipmentDowntimeListInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 停机代码
     */
    private String downtimeCode;

}