package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 设备停机代码表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("equipment_downtime")
public class EquipmentDowntimePO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String DOWNTIME_CODE = "downtime_code";
    public static final String LOSS_TYPE = "loss_type";
    public static final String CODE_TYPE = "code_type";
    public static final String DOWNTIME_REASON = "downtime_reason";
    public static final String REMARK = "remark";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * 主键ID
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂据点
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 停机代码
     */
    @TableField(DOWNTIME_CODE)
    private String downtimeCode;


    /**
     * 损失类型
     */
    @TableField(LOSS_TYPE)
    private String lossType;


    /**
     * 代码类型: 1:通用  2:专用
     */
    @TableField(CODE_TYPE)
    private Integer codeType;


    /**
     * 停机原因
     */
    @TableField(DOWNTIME_REASON)
    private String downtimeReason;


    /**
     * 备注
     */
    @TableField(REMARK)
    private String remark;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
