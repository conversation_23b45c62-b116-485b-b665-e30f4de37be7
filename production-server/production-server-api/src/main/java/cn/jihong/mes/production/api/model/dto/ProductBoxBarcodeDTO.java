package cn.jihong.mes.production.api.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
public class ProductBoxBarcodeDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    private Long id;


    /**
     * 工厂代码
     */
    private String companyCode;


    /**
     * 箱码
     */
    private String barcodeNo;


    /**
     * 是否被绑定  0 否 1是
     */
    private Integer isBand;


    /**
     * 是否使用 0 否 1是
     */
    private Integer isUsed;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 产品名称
     */
    private String productName;


    /**
     * 料号
     */
    private String materialCode;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;


    /**
     * 箱数量
     */
    private BigDecimal boxNum;


    /**
     * 批次识别码
     */
    private String batchCode;


    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    private String machine;


    /**
     * 班次 1 2
     */
    private String shift;


    /**
     * 保质期 默认在当前日期 + 2 年
     */
    private String expirationDate;




}
