package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 生产机台任务操作历史
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-10
 */
@Getter
@Setter
@TableName("product_machine_task_history")
public class ProductMachineTaskHistoryPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String PRODUCT_MACHINE_TASK_ID = "product_machine_task_id";
    public static final String MACHINE_NAME = "machine_name";
    public static final String TYPE = "type";
    public static final String START_TIME = "start_time";
    public static final String END_TIME = "end_time";
    public static final String FINISHED = "finished";
    public static final String REPORTED_PRODUCT_ID = "reported_product_id";
    public static final String REPORTED_QUANTITY = "reported_quantity";
    public static final String REPORTED_UNIT = "reported_unit";

    public static final String CHANGE_VERSION_TYPE = "change_version_type";
    public static final String CHANGE_VERSION_NEW_OLD = "change_version_new_old";
    public static final String CHANGE_VERSION_QUANTITY = "change_version_quantity";
    public static final String CHANGE_VERSION_UNIT = "change_version_unit";

    public static final String REMARK = "remark";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";

    public static final String WORKFLOW_REQUEST_ID = "workflow_request_id";
    public static final String WORKFLOW_REQUEST_STATUS = "workflow_request_status";
    public static final String PRODUCT_OUTBOUND_ID = "product_outbound_id";

    /**
     * 主键id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 机台任务id
     */
    @TableField(PRODUCT_MACHINE_TASK_ID)
    private Long productMachineTaskId;


    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;


    /**
     * 机台任务类型
     */
    @TableField(TYPE)
    private Integer type;


    /**
     * 机台任务开始时间
     */
    @TableField(START_TIME)
    private Date startTime;


    /**
     * 机台任务结束时间
     */
    @TableField(END_TIME)
    private Date endTime;


    /**
     * 是否结束
     */
    @TableField(FINISHED)
    private String finished;


    /**
     * 报工生产工单id
     */
    @TableField(REPORTED_PRODUCT_ID)
    private Long reportedProductId;


    /**
     * 报工数量
     */
    @TableField(REPORTED_QUANTITY)
    private BigDecimal reportedQuantity;

    /**
     * 报工单位
     */
    @TableField(REPORTED_UNIT)
    private String reportedUnit;


    /**
     * 换版类型
     */
    @TableField(CHANGE_VERSION_TYPE)
    private String changeVersionType;

    /**
     * 换版新旧版
     */
    @TableField(CHANGE_VERSION_NEW_OLD)
    private String changeVersionNewOld;

    /**
     * 换版数量
     */
    @TableField(CHANGE_VERSION_QUANTITY)
    private BigDecimal changeVersionQuantity;

    /**
     * 换版单位
     */
    @TableField(CHANGE_VERSION_UNIT)
    private String changeVersionUnit;

    /**
     * OA审批流id
     */
    @TableField(WORKFLOW_REQUEST_ID)
    private Long workflowRequestId;

    /**
     * OA审批流状态
     */
    @TableField(WORKFLOW_REQUEST_STATUS)
    private Integer workflowRequestStatus;

    /**
     * 出站表id
     */
    @TableField(PRODUCT_OUTBOUND_ID)
    private Long productOutboundId;

    /**
     * 备注
     */
    @TableField(REMARK)
    private String remark;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
