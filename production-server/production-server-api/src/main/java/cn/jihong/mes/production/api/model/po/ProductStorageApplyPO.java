package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 入库申请表
 *
 * <AUTHOR>
 * @since 2024-05-17
 */
@Getter
@Setter
@TableName("product_storage_apply")
public class ProductStorageApplyPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PLAN_TICKET_NO = "plan_ticket_no";
    public static final String PALLET_CODE = "pallet_code";
    public static final String CASE_CODE = "case_code";
    public static final String PRODUCED_QUANTITY = "produced_quantity";
    public static final String UNIT = "unit";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String RESPONSE = "response";
    public static final String RESULT = "result";


    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 工程单号
     */
    @TableField(PLAN_TICKET_NO)
    private String planTicketNo;


    /**
     * 栈板码
     */
    @TableField(PALLET_CODE)
    private String palletCode;


    /**
     * 箱码
     */
    @TableField(CASE_CODE)
    private String caseCode;


    /**
     * 已生产
     */
    @TableField(PRODUCED_QUANTITY)
    private BigDecimal producedQuantity;


    /**
     * 单位
     */
    @TableField(UNIT)
    private String unit;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    /**
     * 响应数据
     */
    @TableField(RESPONSE)
    private String response;

    /**
     * 结果 0 失败  1 成功
     */
    @TableField(RESULT)
    private Integer result;


}
