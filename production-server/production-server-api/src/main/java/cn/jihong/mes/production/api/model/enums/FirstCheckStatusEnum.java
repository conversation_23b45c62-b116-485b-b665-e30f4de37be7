package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum FirstCheckStatusEnum {

    CREATE("10", "创建"),
    OPERATOR("20", "操作工"),
    LEADER_OF_PRODUCTION("30", "生产经理"),
    QC("40", "QC"),
    LEADER_OF_QC("50", "QC经理"),
    END("90", "完成"),
    ;

    private String code;

    private String name;

    FirstCheckStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static FirstCheckStatusEnum getFirstCheckStatusEnum(String code) {
        for (FirstCheckStatusEnum value : FirstCheckStatusEnum.values()) {
            if (StringUtils.equals(value.getCode(), code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

}
