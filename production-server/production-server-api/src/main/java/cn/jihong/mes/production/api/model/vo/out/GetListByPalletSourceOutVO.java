package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/8 17:03
 */
@Data
public class GetListByPalletSourceOutVO implements Serializable {


    /**
     * id
     */
    private Long id;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 生产工单id
     */
    private Long productTicketId;


    /**
     * 工序名称
     */
    private String palletSource;


    /**
     * 栈板码
     */
    private String palletCode;




    /**
     * 生产时间
     */
    private Date productionTime;


    /**
     * 生产（报工）数量
     */
    private BigDecimal loadingQuantity;


    /**
     * 单位
     */
    private String unit;


    /**
     * 物料消耗
     */
    private BigDecimal consumptionQuantity;


    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;


    /**
     * 剩余原因
     */
    private String remainingReason;


    /**
     * 上料时间
     */
    private Date loadingTime;


    /**
     * 状态
     */
    private Integer status;

    /**
     * 设备止码
     */
    private Integer machineStopNo;

}
