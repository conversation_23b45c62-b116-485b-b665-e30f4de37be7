package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 网带
 * <AUTHOR>
 * @date 2025-02-24 14:42
 */
@Data
public class MeshDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    private Long id;


    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 工厂代码
     */
    private String workshopCode;

    /**
     *  对应物流网带id
     */
    private String meshId;

    /**
     *  网带类型（1：供料  2：出口  3 备料  4 上料）
     */
    private Integer meshType;

    /**
     * 备注
     */
    private String remark;

}
