package cn.jihong.mes.production.api.model.dto;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ReportSalaryInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String workCode;

    private BigDecimal sumSalary;


    private List<ReportSalaryDayInfo> reportSalaryDayInfoList;

    @Data
    public static class ReportSalaryDayInfo implements Serializable {

        private String reportDate;

        /**
         * 报工薪资
         */
        private BigDecimal totalSalary;

        /**
         * 报工数量
         */
        private BigDecimal totalReportQty;

        /**
         * 核算工资数量
         */
        private BigDecimal totalReportQtyOfWages;
    }


}
