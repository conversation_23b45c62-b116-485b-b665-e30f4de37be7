package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.QualityTraceabilityInVO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialUseDetailsInVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialDetailsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetMaterialUseDetailsOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProduceDetailsOutVO;

import java.util.List;

public interface ICalibreReviewFromMesService {


    List<GetProduceDetailsOutVO> getProduceDetails(QualityTraceabilityInVO boxDTO);

    List<GetProduceDetailsOutVO> getProduceDetails(String planTicketNo);

    List<GetMaterialDetailsOutVO> getMaterialDetails(QualityTraceabilityInVO boxDTO);

    Pagination<GetMaterialUseDetailsOutVO> getMaterialUseDetails(GetMaterialUseDetailsInVO getMaterialUseDetailsInVO);

}
