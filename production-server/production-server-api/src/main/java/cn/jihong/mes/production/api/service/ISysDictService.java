package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.SysDictPO;
import cn.jihong.mes.production.api.model.vo.in.SysDictInVO;
import cn.jihong.mes.production.api.model.vo.in.SysDictQueryInVO;
import cn.jihong.mes.production.api.model.vo.out.SysDictOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 系统字典表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-13
 */
public interface ISysDictService extends IJiHongService<SysDictPO> {

    /**
     * 分页查询字典列表
     *
     * @param queryInVO 查询条件
     * @return 字典列表分页数据
     */
    Pagination<SysDictOutVO> page(SysDictQueryInVO queryInVO);

    /**
     * 根据ID获取字典详情
     *
     * @param id 字典ID
     * @return 字典详情
     */
    SysDictOutVO getSysDictById(Long id);

    /**
     * 新增字典
     *
     * @param dictInVO 字典信息
     * @return 是否成功
     */
    boolean add(SysDictInVO dictInVO);

    /**
     * 修改字典
     *
     * @param dictInVO 字典信息
     * @return 是否成功
     */
    boolean update(SysDictInVO dictInVO);

    /**
     * 删除字典
     *
     * @param id 字典ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 批量删除字典
     *
     * @param ids 字典ID列表
     * @return 是否成功
     */
    boolean batchDelete(List<Long> ids);

    /**
     * 根据字典类型查询字典列表
     *
     * @param dictType 字典类型
     * @return 字典列表
     */
    List<SysDictOutVO> listByType(String dictType);

    SysDictOutVO listByTypeAndCode(String dictType,String dictValue);
}
