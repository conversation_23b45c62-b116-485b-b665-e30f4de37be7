package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum MQBizCodeEnum {

    FIRST_DETECTION(100,"首件"),
    BOX_ACTIVA(200,"箱码激活"),
    ;

    private Integer code;

    private String name;

    MQBizCodeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MQBizCodeEnum getFirstDetection(Integer code){
        for (MQBizCodeEnum value : MQBizCodeEnum.values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        throw new RuntimeException("不存在的任务");
    }
}
