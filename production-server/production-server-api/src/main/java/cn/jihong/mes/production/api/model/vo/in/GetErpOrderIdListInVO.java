package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-11 17:23
 */
@Data
public class GetErpOrderIdListInVO implements Serializable {

    /**
     * 生产计划工单单号
     */
    @NotBlank(message = "生产计划工单单号不能为空")
    private String planTicketNo;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

    /**
     * 工序名称
     */
    @NotBlank(message = "工序名称不能为空")
    private String process;

}
