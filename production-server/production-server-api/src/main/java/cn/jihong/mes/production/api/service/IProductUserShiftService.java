package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.po.ProductUserShiftPO;
import cn.jihong.mes.production.api.model.vo.in.ProductShiftInVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import javax.validation.Valid;

/**
 * 员工班组信息表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
public interface IProductUserShiftService extends IJiHongService<ProductUserShiftPO> {

    Long saveProductShift(@Valid ProductShiftInVO productShiftInVO);

    ProductShiftInVO getProductShift();

    void updateProductShift(@Valid ProductShiftInVO productShiftInVO);
}
