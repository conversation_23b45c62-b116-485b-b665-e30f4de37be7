package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

/**
 * 网带类型枚举
 * 用于物料处理操作的类型定义
 */
@Getter
public enum MeshTypeEnum {

    SUPPLY(1, "供料"),
    OUTLET(2, "出口"),
    PREPARE(3, "备料"),
    FEEDING(4, "上料"),
    ;

    private Integer code;

    private String name;

    MeshTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取对应的枚举值
     * @param code 枚举编码
     * @return 对应的枚举值
     * @throws RuntimeException 当code不存在时抛出异常
     */
    public static MeshTypeEnum getMeshTypeEnum(Integer code) {
        for (MeshTypeEnum value : MeshTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的网带类型: " + code);
    }

    /**
     * 根据name获取对应的枚举值
     * @param name 枚举名称
     * @return 对应的枚举值
     * @throws RuntimeException 当name不存在时抛出异常
     */
    public static MeshTypeEnum getMeshTypeEnumByName(String name) {
        for (MeshTypeEnum value : MeshTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的网带类型: " + name);
    }

    /**
     * 检查给定的code是否有效
     * @param code 枚举编码
     * @return 如果code有效返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        try {
            getMeshTypeEnum(code);
            return true;
        } catch (RuntimeException e) {
            return false;
        }
    }
}
