package cn.jihong.mes.production.api.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BasePaperDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 个数
     */
    private BigDecimal pcs;
    /**
     * 米数
     */
    private BigDecimal numberOfRice;

    /**
     * 工程单号
     */
    private String planTicketNo;

    private PaperInfo paperInfo;

    @Data
    public static class PaperInfo implements Serializable{
        private static final long serialVersionUID = 1L;

        /**
         * 幅宽
         */
        private BigDecimal width;

        /**
         * 克重
         */
        private BigDecimal grammage;

    }



}
