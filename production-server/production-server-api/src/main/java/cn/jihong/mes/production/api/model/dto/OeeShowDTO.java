package cn.jihong.mes.production.api.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

@Data
public class OeeShowDTO implements Serializable {


    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 生产日期
     */
    private String produceDate;



    /**
     * 良品率
     */
    private BigDecimal goodProductRate;


    /**
     * 良品数
     */
    private BigDecimal totalReportedQuantity;

    /**
     * 不良良品数
     */
    private BigDecimal totalDefectiveProductQuantity;



    /**
     * 运行时长（分钟）
     */
    private BigDecimal totalDurationMinutes;

    /**
     * 标准产能
     */
    private BigDecimal standardProductionCapacity;
    
}
