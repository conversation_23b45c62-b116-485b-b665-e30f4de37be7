package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:27
 */
@Data
public class SaveMachineTaskInVO implements Serializable {

    private static final long serialVersionUID = -27039779959847876L;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

    /**
     * 机台任务类型: 1-生产 2-调机 3-会议
     */
    @NotNull(message = "任务类型不能为空")
    private Integer type;

    /**
     * 原来的机台状态类型
     */
    private Integer originalType;


    /**
     * 报工生产工单id 如果类型是生产则必传
     */
    private Long reportedProductId;


    /**
     * 报工数量
     */
    private BigDecimal reportedQuantity;

    /**
     * 计件类型
     */
    private String pieceType;

    /**
     * 换版类型
     */
    private String changeVersionType;

    /**
     * 换版新旧版
     */
    private String changeVersionNewOld;

    /**
     * 换版数量
     */
    private BigDecimal changeVersionQuantity;

    /**
     * 报工班次
     */
    private Integer reportShift;

    /**
     * 超产原因
     */
    private String reason;

    /**
     * 出站表id
     */
    private Long productOutboundId;


}
