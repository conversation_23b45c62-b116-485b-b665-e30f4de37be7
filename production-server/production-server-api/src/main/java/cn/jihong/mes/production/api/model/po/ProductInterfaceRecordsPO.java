package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口调用记录
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
@Getter
@Setter
@TableName("product_interface_records")
public class ProductInterfaceRecordsPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String PRODUCT_TICKET_ID = "product_ticket_id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MAIN_INFO = "main_info";
    public static final String BUSINESS_TYPE = "business_type";
    public static final String RESPONSE = "response";
    public static final String RESULT = "result";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String PLAN_TICKET_NO = "plan_ticket_no";


    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 生产工单id
     */
    @TableField(PRODUCT_TICKET_ID)
    private Long productTicketId;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 主要信息
     */
    @TableField(MAIN_INFO)
    private String mainInfo;


    /**
     * 业务类型 1 良品报工  2 不良品报工  3 更新物料  4 入库申请
     */
    @TableField(BUSINESS_TYPE)
    private Integer businessType;


    /**
     * 响应数据
     */
    @TableField(RESPONSE)
    private String response;

    /**
     * 结果 0 失败  1 成功
     */
    @TableField(RESULT)
    private Integer result;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    /**
     * 生产工程单号
     */
    @TableField(PLAN_TICKET_NO)
    private String planTicketNo;

}
