package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 设备停机工序关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Getter
@Setter
@TableName("equipment_downtime_process")
public class EquipmentDowntimeProcessPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String EQUIPMENT_DOWNTIME_ID = "equipment_downtime_id";
    public static final String PROCESS_CODE = "process_code";
    public static final String PROCESS_NAME = "process_name";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String DELETED = "deleted";



    /**
     * 主键ID
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 设备停机代码id
     */
    @TableField(EQUIPMENT_DOWNTIME_ID)
    private Long equipmentDowntimeId;


    /**
     * 工序编码
     */
    @TableField(PROCESS_CODE)
    private String processCode;


    /**
     * 工序名称
     */
    @TableField(PROCESS_NAME)
    private String processName;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private LocalDateTime createTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
