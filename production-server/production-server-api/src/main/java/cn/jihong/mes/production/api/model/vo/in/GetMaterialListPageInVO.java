package cn.jihong.mes.production.api.model.vo.in;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetMaterialListPageInVO extends PageRequest implements Serializable {

    private Long id;


    /**
     * 公司code
     */
    private String companyCode;

    /**
     * 工程单号
     */
    private String planTicketNo;

    /**
     * 物料类别
     */
    private String materialType;

    /**
     * 采购批次
     */
    private String purchaseBatch;


    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料状态
     */
    private String materialStatus;

    /**
     * 核销状态
     */
    private String writeOffStatus;




}
