package cn.jihong.mes.production.api.model.enums;

import cn.jihong.common.model.resultcode.IResultCode;
import lombok.Getter;

@Getter
public enum SystemResultCode implements IResultCode {

    DOWN_MATERIAL_OVER_CLAIMED(-10001, "下料ERP申请量超过工单总应发", false),


    ;
    /** 操作代码  */
    private int code;

    /** 提示信息  */
    private String message;

    /** 响应业务状态 */
    private boolean state;

    SystemResultCode(int code, String message, boolean state) {
        this.code = code;
        this.message = message;
        this.state = state;
    }

    @Override
    public int code() {
        return this.code;
    }

    @Override
    public String message() {
        return this.message;
    }

    @Override
    public boolean state() {
        return this.state;
    }
}
