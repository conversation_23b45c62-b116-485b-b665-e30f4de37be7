package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.EquipmentDowntimeProcessPO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeProcessInVO;
import cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeProcessOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;


/**
 * 设备停机工序关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IEquipmentDowntimeProcessService extends IJiHongService<EquipmentDowntimeProcessPO> {

        /**
         * 保存
         * @param inVO
         * @return: Boolean
         */
        Boolean saveEquipmentDowntimeProcess(EquipmentDowntimeProcessInVO inVO);


        List<EquipmentDowntimeProcessPO> getListByEquipmentDowntimeId(Long equipmentDowntimeId);

        /**
         * 批量删除
         * @param ids
         * @return: Boolean
         */
        Boolean deleteByIds(String ids);


        /**
         * 获取详情
         * @param id
         * @return: EquipmentDowntimeProcessOutVO
         */
        EquipmentDowntimeProcessOutVO getSingleEquipmentDowntimeProcessById(Long id);
}
