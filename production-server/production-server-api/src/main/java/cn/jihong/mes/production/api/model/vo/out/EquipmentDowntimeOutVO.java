package cn.jihong.mes.production.api.model.vo.out;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 设备停机代码表
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
public class EquipmentDowntimeOutVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工厂据点
     */
    private String companyCode;

    /**
     * 停机代码
     */
    private String downtimeCode;

    /**
     * 损失类型
     */
    private String lossType;

    /**
     * 代码类型: 1:通用  2:专用
     */
    private Integer codeType;

    /**
     * 停机原因
     */
    private String downtimeReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 编辑人
     */
    private Long updateBy;

    /**
     * 编辑时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;
}