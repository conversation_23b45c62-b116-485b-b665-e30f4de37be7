package cn.jihong.mes.production.api.model.vo.in;


import java.io.Serializable;
import java.time.LocalDateTime;

import cn.jihong.common.model.dto.PageRequest;
import lombok.Getter;
import lombok.Setter;


/**
 * 设备停机代码表
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
public class EquipmentDowntimePageInVO extends PageRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工厂据点
     */
    private String companyCode;

    /**
     * 停机代码
     */
    private String downtimeCode;

    /**
     * 损失类型
     */
    private String lossType;

    /**
     * 代码类型: 1:通用  2:专用
     */
    private Integer codeType;

    /**
     * 停机原因
     */
    private String downtimeReason;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 编辑人
     */
    private Long updateBy;

    /**
     * 编辑时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;
}