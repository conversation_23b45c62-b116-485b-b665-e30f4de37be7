package cn.jihong.mes.production.api.model.dto;

import cn.jihong.common.model.dto.BaseWorkflowFormDataDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-09-11 14:43
 */
@Data
public class DownMaterialFlowDTO extends BaseWorkflowFormDataDTO implements Serializable {

    /**
     * 流程请求ID
     */
    private Long workRequestId;


    /**
     * 请求标题
     */
    private String requestTitle;

    /**
     * 公司据点
     */
    private String companySite;


    /**
     * 用户ID
     */
    private Long userId;


    /**
     * 工号
     */
    private String workcode;


    /**
     * 名字
     */
    private String name;


    /**
     * 公司/分部ID
     */
    private Long companyId;


    /**
     * 部门ID
     */
    private Long deptId;


    /**
     * 部门名称
     */
    private String deptName;


    /**
     * 公司/分部名
     */
    private String companyName;

    /**
     * 岗位ID
     */
    private Long jobTitleId;

    private String jobTitleName;


    /**
     * 申请日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate applyDate;


    /**
     * 申请人ID
     */
    private Long applicantUserId;


    /**
     * 申请人工号
     */
    private String applicantWorkCode;


    /**
     * 申请人部门
     */
    private Long applicantDepartmentId;


    /**
     * 申请人岗位
     */
    private Long applicantJobTitleId;


    /**
     * 紧急程度
     */
    private Integer requestLevel;


    /**
     * 状态
     */
    private String requestStatus;

    private Integer hasIt;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 更新人
     */
    private String updateBy;


    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;


    /**
     * 开单数量
     */
    private BigDecimal kdsl;

    /**
     * flag
     */
    private Integer flag;

    /**
     * 申领数量
     */
    private BigDecimal slsl;

    /**
     * 单位
     */
    private String dw;


    /**
     * 申领原因
     */
    private String slyy;

    /**
     * 产品名称
     */
    private String cpmc;

    /**
     * 物料名称
     */
    private String wlmc;

    /**
     * 申请人工号
     */
    private String sqr;

    /**
     * 工单号
     */
    private String mesno;

    /**
     * 厦门生产一部
     */
    private String xmscyb;

    /**
     * 厦门生产二部
     */
    private String xmsceb;

    /**
     * 廊坊一期
     */
    private String lfyq;

    /**
     * 廊坊二期
     */
    private String lfeq;

    /**
     * 业务ID
     */
    private Long ywID;
}
