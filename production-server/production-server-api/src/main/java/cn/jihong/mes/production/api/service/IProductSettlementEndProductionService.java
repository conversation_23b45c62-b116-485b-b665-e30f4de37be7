package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductSettlementEndProductionPO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementEndProductionCollectDetailInVO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementEndProductionDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionCollectDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionListOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工程结算产成品信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
public interface IProductSettlementEndProductionService extends IJiHongService<ProductSettlementEndProductionPO> {

    /**
     * 汇总工程结算产成品信息
     * @param productTicketNo
     * @param productTicketId
     * @return: void
     * <AUTHOR>
     * @date: 2023/11/13 15:36
     */
    void collect(String productTicketNo, Long productTicketId, Map<String,Integer> processSeqMap);


    /**
     * 查询产成品信息
     * @param productTicketNo
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetSettlementEndProductionListOutVO>
     * <AUTHOR>
     * @date: 2023/11/8 16:05
     */
    List<GetSettlementEndProductionListOutVO> getSettlementEndProductionList(String productTicketNo);



    Pagination<GetSettlementEndProductionCollectDetailOutVO> getSettlementEndProductionCollectDetail(GetSettlementEndProductionCollectDetailInVO inVO);


    /**
     * 查询产成品详情信息列表
     * @param inVO
     * @return: cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.po.ProductPalletPO>
     * <AUTHOR>
     * @date: 2023/11/14 18:05
     */
     Pagination<ProductPalletPO> getSettlementEndProductionDetail(GetSettlementEndProductionDetailInVO inVO);


}
