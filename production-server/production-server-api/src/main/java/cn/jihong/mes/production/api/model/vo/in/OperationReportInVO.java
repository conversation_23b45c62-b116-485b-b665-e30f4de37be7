package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:27
 */
@Data
public class OperationReportInVO implements Serializable {

    private static final long serialVersionUID = -27039779959847876L;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

    /**
     * 机台任务类型: 1-生产 2-调机 3-会议
     */
    @NotNull(message = "任务类型不能为空")
    private Integer type;

    /**
     * 原来的机台状态类型
     */
    private Integer originalType;

    /**
     * 设备停机ID
     */
    private Long equipmentDowntimeId;

    /**
     * 设备停机编码
     */
    private String equipmentDowntimeCode;

    /**
     * 损失类型
     */
    private String lossType;

    /**
     * 工单id
     */
    private Long productTicketId;

    /**
     * 是否结束当前任务 0：否 1：是
     */
    private String finished;



}
