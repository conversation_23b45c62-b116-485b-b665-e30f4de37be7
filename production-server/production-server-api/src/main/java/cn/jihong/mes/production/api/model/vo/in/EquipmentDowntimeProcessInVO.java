package cn.jihong.mes.production.api.model.vo.in;



import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 设备停机工序关联表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Getter
@Setter
public class EquipmentDowntimeProcessInVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 设备停机代码id
     */
    private Long equipmentDowntimeId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否删除
     */
    private Boolean deleted;
}