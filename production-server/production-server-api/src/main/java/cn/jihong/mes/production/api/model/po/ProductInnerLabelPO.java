package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 生产内标签表
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Getter
@Setter
@TableName("product_inner_label")
public class ProductInnerLabelPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String CUSTOMER_NO = "customer_no";
    public static final String CUSTOMER_NAME = "customer_name";
    public static final String PLAN_TICKET_NO = "plan_ticket_no";
    public static final String PRODUCT_NAME = "product_name";
    public static final String MATERIAL_CODE = "material_code";
    public static final String BARCODE_TOTAL = "barcode_total";
    public static final String PRODUCTION_DATE = "production_date";
    public static final String EXPIRATION_DATE = "expiration_date";
    public static final String TEMPLATE_ID = "template_id";
    public static final String TEMPLATE_NAME = "template_name";
    public static final String PRINT_STATUS = "print_status";
    public static final String DISABLE_STATUS = "disable_status";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";



    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 客户编号
     */
    @TableField(CUSTOMER_NO)
    private String customerNo;


    /**
     * 客户名称
     */
    @TableField(CUSTOMER_NAME)
    private String customerName;


    /**
     * 生产工程单号
     */
    @TableField(PLAN_TICKET_NO)
    private String planTicketNo;


    /**
     * 产品名称
     */
    @TableField(PRODUCT_NAME)
    private String productName;


    /**
     * 料号
     */
    @TableField(MATERIAL_CODE)
    private String materialCode;


    /**
     * 箱码总数量
     */
    @TableField(BARCODE_TOTAL)
    private Long barcodeTotal;


    /**
     * 生产日期
     */
    @TableField(PRODUCTION_DATE)
    private Date productionDate;


    /**
     * 保质期 默认在当前日期 + 2 年
     */
    @TableField(EXPIRATION_DATE)
    private Date expirationDate;

    /**
     * 模板ID
     */
    @TableField(TEMPLATE_ID)
    private String templateId;

    /**
     * 模板名称
     */
    @TableField(TEMPLATE_NAME)
    private String templateName;


    /**
     * 打印状态 0 未打印  1 已打印
     */
    @TableField(PRINT_STATUS)
    private Integer printStatus;


    /**
     * 作废状态 0 作废   1 未作废
     */
    @TableField(DISABLE_STATUS)
    private Integer disableStatus;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
