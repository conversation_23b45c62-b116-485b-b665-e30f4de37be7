package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

@Getter
public enum SiteEnum {

    XGGC("SITE-20", "孝感工厂"),
    LFEQ("SITE-082", "廊坊二期"),
    LFYQ("SITE-08", "廊坊一期"),
    XMGC("SITE-01", "厦门工厂"),
    XMBZ("SITE-22", "厦门包装工业"),
    AHGC("SITE-16", "安徽工厂"),
    HSXC("SITE-06", "呼市新厂"),

    ;

    private String code;

    private String name;

    SiteEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SiteEnum getCompanyBoxEnum(String code) {
        for (SiteEnum value : SiteEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的据点");
    }



}
