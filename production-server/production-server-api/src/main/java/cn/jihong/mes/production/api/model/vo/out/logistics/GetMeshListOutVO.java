package cn.jihong.mes.production.api.model.vo.out.logistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:18
 */
@Data
public class GetMeshListOutVO implements Serializable {

    /**
     * id
     */
    private Long id;


    /**
     * 工厂代码
     */
    private String companyCode;

    /**
     * 车间代码
     */
    private String workshopCode;

    /**
     *  对应物流网带id
     */
    private String meshId;

    /**
     *  网带类型（1：供料  2：出口）
     */
    private int meshType;


    /**
     * 备注
     */
    private String remark;


}
