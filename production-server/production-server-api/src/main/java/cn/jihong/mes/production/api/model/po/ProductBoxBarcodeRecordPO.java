package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 条码操作记录
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Getter
@Setter
@TableName("product_box_barcode_record")
public class ProductBoxBarcodeRecordPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String PRODUCT_BARCODE_ID = "product_barcode_id";
    public static final String PRODUCT_BARCODE_DETAIL_ID = "product_barcode_detail_id";
    public static final String OPERATION_TYPE = "operation_type";
    public static final String BARCODE_NO_START = "barcode_no_start";
    public static final String BARCODE_NO_END = "barcode_no_end";
    public static final String BARCODE_TOTAL = "barcode_total";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";


    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 箱码号段id
     */
    @TableField(PRODUCT_BARCODE_ID)
    private Long productBarcodeId;


    /**
     * 箱码号段明细id
     */
    @TableField(PRODUCT_BARCODE_DETAIL_ID)
    private Long productBarcodeDetailId;


    /**
     * 操作类型 10 打印  20 补打
     */
    @TableField(OPERATION_TYPE)
    private String operationType;


    /**
     * 箱码开始号
     */
    @TableField(BARCODE_NO_START)
    private String barcodeNoStart;


    /**
     * 箱码截止号
     */
    @TableField(BARCODE_NO_END)
    private String barcodeNoEnd;


    /**
     * 箱码总数量
     */
    @TableField(BARCODE_TOTAL)
    private Long barcodeTotal;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
