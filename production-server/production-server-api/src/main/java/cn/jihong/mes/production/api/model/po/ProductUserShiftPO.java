package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 员工班组信息表
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Getter
@Setter
@TableName("product_user_shift")
public class ProductUserShiftPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String USER_ID = "user_id";
    public static final String TEAM_USERS = "team_users";
    public static final String ROLE_NAME = "role_name";
    public static final String ROLE_CODE = "role_code";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";


    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 用户id
     */
    @TableField(USER_ID)
    private Long userId;


    /**
     * 班组人员id
     */
    @TableField(TEAM_USERS)
    private String teamUsers;


    /**
     * 角色/岗位名称
     */
    @TableField(ROLE_NAME)
    private String roleName;


    /**
     * 角色/岗位Code
     */
    @TableField(ROLE_CODE)
    private String roleCode;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
