package cn.jihong.mes.production.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 箱码号段明细
 *
 * <AUTHOR>
 * @since 2024-09-10
 */
@Data
public class ProductBarcodeSplitBarcodeDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    private Long id;

    private String wmsBarcodeStoreId;

    /**
     * 工厂代码
     */
    private String companyCode;


    /**
     * 箱码
     */
    private String barcodeNo;
    private String wmsBarcodeNo;

    /**
     * 物料编号
     */
    private String itemNumber;


    /**
     * 物品名称
     */
    private String itemName;


    /**
     * 规格
     */
    private String itemSpece;


    /**
     * 日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date itemDate;


    /**
     * 单位
     */
    private String itemUnit;
    private String itemUnitName;


    /**
     * 数量
     */
    private BigDecimal itemQuantity;

    /**
     * 仓库
     */
    private String warehouse;
    private String warehouseName;


    /**
     * 来源单号
     */
    private String sourceNo;

    /**
     * 单包数量
     */
    private Long itemNumberOfPacks;


    /**
     * 启用状态  0 未启用  1 已启用
     */
    private Integer enableStatus;


    /**
     * 打印状态 0 未打印  1 已打印
     */
    private Integer printStatus;


    /**
     * 创建人
     */
    private Long createBy;
    private String createName;


    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 编辑人
     */
    private Long updateBy;


    /**
     * 编辑时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
