<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.EquipmentDowntimeMapper">

    <select id="getEquipmentDowntimePage"
            resultType="cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO">
        select t1.*,t2.process_code,t2.process_name from equipment_downtime t1
        left join equipment_downtime_process t2 on t2.equipment_downtime_id = t1.id
        where t1.deleted = 0
        order by t1.downtime_code asc
    </select>


    <select id="getOeeEquipmentDowntimeList"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetOeeEquipmentDowntime">

        select
            t.loss_type,
            count(1) as down_time_num,
            ROUND(SUM(IFNULL(t.reported_quantity, 0)), 2)  AS total_reported_quantity,
            ROUND(SUM(IFNULL(t.defective_product, 0)), 2)  as total_defective_product_quantity,
            ROUND(SUM(IFNULL(t.duration_minutes, 0)), 2) as down_time_minters
        from (
                 select
                     pmth.id,
                     pmth.reported_product_id,
                     pm.company_code,
                     pmth.machine_name,
                     pt.process,
                     pt.produce_date,
                     pt.plan_ticket_no,
                     pmth.start_time,
                     pmth.end_time,
                     pt.real_product,
                     pt.defective_product,
                     pmth.reported_quantity,
                     case when ROUND(TIMESTAMPDIFF(SECOND, pmth.start_time, pmth.end_time) / 60.0, 2) > (24*60) then (24*60) else
                         ROUND(TIMESTAMPDIFF(SECOND, pmth.start_time, pmth.end_time) / 60.0, 2) end AS duration_minutes,
                     pmth.loss_type
                 from
                     product_machine_task_history pmth
                         inner join (
                         select
                             id,
                             erp_machine_name,
                             company_code,
                             row_number() over (
        partition by erp_machine_name
        order by
        id desc
        ) as rn
         from production_machine)  pm on pm.erp_machine_name = pmth.machine_name and pm.rn = 1
         left join product_ticket pt on pt.id = pmth.reported_product_id and pmth.finished = 1
         left join production_shift_set pss on pss.company_code = pt.company_code and pss.is_deleted = 0
         left join production_shift_detail psd on pss.id = psd.main_id and psd.serial_no = pt.shift and psd.is_deleted = 0
         where  pmth.deleted = 0
        <if test="inVO.shift != '' and inVO.shift != null">
            AND psd.serial_no = #{inVO.shift}
        </if>
        <if test="inVO.machineName != '' and inVO.machineName != null">
            AND pt.machine_name = #{inVO.machineName}
        </if>
        <if test="inVO.processName != '' and inVO.processName != null">
            AND pt.process = #{inVO.processName}
        </if>
--         and pmth.loss_type is not null
                 order by pt.company_code
             ) t
        group by t.loss_type having count(1)

    </select>
</mapper>
