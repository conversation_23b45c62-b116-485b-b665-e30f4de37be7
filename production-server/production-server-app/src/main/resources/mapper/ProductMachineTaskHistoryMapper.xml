<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductMachineTaskHistoryMapper">

    <select id="getProductMachineTaskHistoryPage"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetProductMachineTaskHistoryPageOutVO">
        SELECT
            t1.id as machine_task_history_id,
            t1.create_by as reported_user_id,
            t2.team_users,
            t2.process,
            t2.produce_date,
            t2.shift,
            t1.machine_name,
            t1.reported_quantity,
            t1.create_time as reported_time,
            t2.piece_type,
            t1.workflow_request_id,
            t1.workflow_request_status
        FROM
            product_machine_task_history t1
                JOIN product_ticket t2 ON t1.reported_product_id = t2.id
        WHERE
            t1.reported_quantity IS NOT NULL
          AND t1.reported_quantity > 0
          AND t2.company_code = #{companyCode}
          AND t2.id = #{inVO.productTicketId}
    ORDER BY t1.create_time ASC
    </select>


    <select id="getChangeVersionHistoryPage"
            resultType="cn.jihong.mes.production.api.model.vo.out.GetChangeVersionHistoryPageOutVO">
        SELECT
            t1.id as machine_task_history_id,
            t1.machine_name,
            t1.create_by as reported_user_id,
            t1.create_time as reported_time,
            t1.change_version_type,
            t1.change_version_new_old,
            t1.change_version_quantity,
            t1.change_version_unit,
            t2.process,
            t2.produce_date,
            t2.shift
        FROM
            product_machine_task_history t1
                JOIN product_ticket t2 ON t1.reported_product_id = t2.id
        WHERE
            t1.type = -1101
          AND t1.change_version_quantity IS NOT NULL
          AND t2.company_code = #{companyCode}
          AND t2.id = #{inVO.productTicketId}
        ORDER BY t1.create_time DESC
    </select>
</mapper>
