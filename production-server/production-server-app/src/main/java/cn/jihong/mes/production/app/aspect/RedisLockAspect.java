package cn.jihong.mes.production.app.aspect;

import cn.jihong.common.exception.CommonException;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Aspect
@Component
public class RedisLockAspect {

    private final Logger log = LoggerFactory.getLogger(RedisLockAspect.class);

    @Autowired
    private RedissonClient redissonClient;

    @Pointcut("@annotation(redisLock) && args(redisLockKey, ..)")
    public void lockMethod(RedisLock redisLock, String redisLockKey) {}

    @Around(value = "lockMethod(redisLock, redisLockKey)", argNames = "proceedingJoinPoint, redisLock, redisLockKey")
    public Object executeWithLock(ProceedingJoinPoint proceedingJoinPoint, RedisLock redisLock, String redisLockKey)
        throws Throwable {
        String lockKey = redisLockKey;
        if (StringUtils.isBlank(lockKey)) {
            log.error("没有设置redisKey，不执行加锁动作");
            return proceedingJoinPoint.proceed();
        }
        log.info("进入方法: {}", lockKey);
        long expire = redisLock.expire();
        TimeUnit timeUnit = redisLock.timeUnit();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean lockAcquired = lock.tryLock(0L, expire, timeUnit);
            if (lockAcquired) {
                try {
                    log.info("获得锁: {}", lockKey);
                    return proceedingJoinPoint.proceed();
                } finally {
                    log.info("释放锁: {}", lockKey);
                    lock.unlock();
                }
            } else {
                throw new CommonException("操作正在进行，请勿重复操作");
            }
        } catch (Exception ex) {
            throw new CommonException("操作处理失败：" + ex.getMessage(),ex);
        }
    }

}
