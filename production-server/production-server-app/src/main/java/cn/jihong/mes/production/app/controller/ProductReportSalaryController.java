package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.dto.ReportSalaryInfoDTO;
import cn.jihong.mes.production.api.service.IProductReportSalaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *报工薪资
 */
@Slf4j
@RestController
@RequestMapping("/reportSalary")
@ShenyuSpringMvcClient(path = "/reportSalary/**")
public class ProductReportSalaryController {

    @Resource
    private IProductReportSalaryService productReportSalaryService;

    /**
     * 查询当天及当月的薪资
     */
    @GetMapping("/getReportSalary")
    public StandardResult<ReportSalaryInfoDTO> getReportSalary(){
        return StandardResult.resultCode(OperateCode.SUCCESS,productReportSalaryService.getReportSalary());
    }


}
