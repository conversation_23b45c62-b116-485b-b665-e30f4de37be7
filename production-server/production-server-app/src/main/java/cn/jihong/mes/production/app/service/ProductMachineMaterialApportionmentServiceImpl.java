package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.annotation.RecordMethodTime;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.vo.MachineConfigInVO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineConfigService;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.constant.BasePaperConst;
import cn.jihong.mes.production.api.model.dto.*;
import cn.jihong.mes.production.api.model.enums.MaterialUseTypeEnum;
import cn.jihong.mes.production.api.model.enums.SiteEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialApportionmentPO;
import cn.jihong.mes.production.api.model.po.ProductMachineMaterialRecordPO;
import cn.jihong.mes.production.api.model.po.ProductMachinePartsPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialDailySettlementInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductUseMaterialsInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.mapper.ProductMachineMaterialApportionmentMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.dto.ImaaTNewDTO;
import cn.jihong.oa.erp.api.model.dto.ReportUnitConvertDTO;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.model.vo.ImaalTVO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.model.vo.SfbaTVO;
import cn.jihong.oa.erp.api.service.*;
import cn.jihong.tms.api.model.enums.TicketStatusEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机台物料消耗使用信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Slf4j
@DubboService
public class ProductMachineMaterialApportionmentServiceImpl
    extends JiHongServiceImpl<ProductMachineMaterialApportionmentMapper, ProductMachineMaterialApportionmentPO>
    implements IProductMachineMaterialApportionmentService {

    @Resource
    private IProductMachineMaterialRecordService productMachineMaterialRecordService;
    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductMachineDayService productMachineDayService;
    @Resource
    private IProductMachinePartsService productMachinePartsService;
    @DubboReference
    private IProductionMachineConfigService productionMachineConfigService;
    @DubboReference
    private ISfbaTService sfbaTService;
    @DubboReference
    private IImaalTService imaalTService;
    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IReportUnitConvertService reportUnitConvertService;
    @DubboReference
    private IImaaTNewService imaaTNewService;
    @DubboReference
    private IEcaaucTService ecaaucTService;
    @DubboReference
    private IProductionMachineService productionMachineService;

    @Override
    public List<GetMaterialDailySettlementOutVO>
        getMaterialDailyApportionment(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {

        // 查询是否已经分摊了，
        ProductMachineDayOutVO productMachineDay = productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO);

        if (productMachineDay == null) {
            // 未找到日结信息，则返回空列表
            throw new CommonException("未找到日结信息，请先进行日结！");
        }

        if (productMachineDay.getIsFinish() == null || Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productMachineDay.getIsFinish())) {
            throw new CommonException("日结未完成，请先完成日结！");
        }

        // 未分摊则返回需要分摊的信息
        if (productMachineDay == null || Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productMachineDay.getIsApportionment())) {
            return apportionmentMaterialDaily(getMaterialDailySettlementInVO);
        } else {
            // 已分摊则读取数据库信息
            return  baseMapper.getMaterialDailyApportionment(getMaterialDailySettlementInVO);
        }
    }

    @Override
    public List<GetMaterialDailySettlementOutVO>
    apportionmentMaterialDaily(GetMaterialDailySettlementInVO settlementInput){
        // 日志记录输入参数
        log.info("开始进行物料日分摊，输入参数: {}", settlementInput);
        StringBuffer sb = new StringBuffer();

        // ==================================任务信息=================================
        // 查询机台当天的任务信息汇总
        List<GetOneDayTaskSummaryOutVO> taskSummary = productTicketService.getOneDayTaskSummary(
                settlementInput.getMachineName(), settlementInput.getProduceDate(), settlementInput.getShift());
        sb.append("查询机台当天的任务信息汇总: " + JSON.toJSONString( taskSummary) + "\n");

        // 生产任务的良品和不良品总数
        Map<Long, BigDecimal> taskQuantityMap =
                taskSummary.stream().collect(Collectors.toMap(GetOneDayTaskSummaryOutVO::getTaskId,
                        summary -> summary.getReportedQuantity().add(summary.getUnconfirDefectiveQuantity())));
        sb.append("生产的良品和不良品总数: " + JSON.toJSONString(taskQuantityMap) + "\n");

        // ====================================工单标准消耗==============================================
        Map<String, ApportionmentMaterialStandardConsumptionDTO> ticketMaterialPlacesMap = getTicketStandardConsumptionMap( taskSummary);
        
        // =========================================数据 ==============================
        // 查询汇总后的物料信息
        ProductMachineMaterialRecordDTO recordQuery = new ProductMachineMaterialRecordDTO();
        recordQuery.setMachineName(settlementInput.getMachineName());
        recordQuery.setProduceDate(settlementInput.getProduceDate());
        recordQuery.setShift(settlementInput.getShift());
        List<ProductMachineMaterialRecordPO> consumptionRecords =
                productMachineMaterialRecordService.getConsumptionQuantityNeedDistribution(recordQuery)
                        .stream().filter(record -> record.getMaterialCode().startsWith(BasePaperConst.PAPER_TYPE)).collect(Collectors.toList());

        sb.append("获取的消耗记录: " + JSON.toJSONString(consumptionRecords) + "\n");

        // =====================================封装消耗信息======================================
        List<ApportionmentMaterialDailyDTO> apportionmentMaterialDailyDTOList =
                getApportionmentMaterialDailyDTOS(sb, taskSummary, taskQuantityMap, ticketMaterialPlacesMap,consumptionRecords);
        if (CollectionUtils.isEmpty(apportionmentMaterialDailyDTOList)){
            return Lists.newArrayList();
        }

        // =========================================物料使用信息========================================
        consumptionRecords = getProductMachineMaterialRecordPOS(settlementInput, sb, apportionmentMaterialDailyDTOList,consumptionRecords);

        // ===========================================按照物料编码进行分摊===================================================
        List<GetMaterialDailySettlementOutVO> list = getGetMaterialDailySettlementOutVOS(apportionmentMaterialDailyDTOList, consumptionRecords,sb);

        log.info("工单消耗日志：{}", sb.toString());
        return list;
    }

    @RecordMethodTime(msg = "获取工单标准消耗")
    @Override
    public Map<String,ApportionmentMaterialStandardConsumptionDTO> getTicketStandardConsumptionMap(List<GetOneDayTaskSummaryOutVO> taskSummary) {
        List<String> planTicketNos =
                taskSummary.stream().map(GetOneDayTaskSummaryOutVO::getPlanTicketNo).collect(Collectors.toList());
        Map<String, List<SfbaTVO>> productTicketNosMap = sfbaTService.getByProductTicketNos(planTicketNos);

        log.info("获取的工单信息映射: {}", productTicketNosMap);


        Map<String, String> productNameMap = taskSummary.stream().collect(Collectors.toMap(GetOneDayTaskSummaryOutVO::getPlanTicketNo, GetOneDayTaskSummaryOutVO::getProductName, (a, b) -> a));

        Map<String,ApportionmentMaterialStandardConsumptionDTO> apportionmentMaterialStandardConsumptionDTOS = Maps.newConcurrentMap();
        productTicketNosMap.forEach((ticketNo, sfbaTVOs) -> {
            ApportionmentMaterialStandardConsumptionDTO apportionmentMaterialStandardConsumptionDTO = new ApportionmentMaterialStandardConsumptionDTO();
            apportionmentMaterialStandardConsumptionDTO.setPlanTicketNo(ticketNo);
            apportionmentMaterialStandardConsumptionDTO.setProductName(productNameMap.get(ticketNo));
            Map<String, List<SfbaTVO>> processMap = sfbaTVOs.stream().collect(Collectors.groupingBy(SfbaTVO::getSfba003, Collectors.toList()));

            List<ApportionmentMaterialStandardConsumptionDTO.ProcessDTO> processDTOs = new ArrayList<>();
            processMap.entrySet().forEach(entry -> {
                String process = entry.getKey();
                List<SfbaTVO> sfbaTVOList = entry.getValue();

                ApportionmentMaterialStandardConsumptionDTO.ProcessDTO processDTO = new ApportionmentMaterialStandardConsumptionDTO.ProcessDTO();
                processDTO.setProcessCode(process);
                List<ApportionmentMaterialStandardConsumptionDTO.ProcessDTO.MaterialDTO> materialDTOS = new ArrayList<>();
                sfbaTVOList.stream().forEach(sfbaTVO -> {
                    ApportionmentMaterialStandardConsumptionDTO.ProcessDTO.MaterialDTO materialDTO = new ApportionmentMaterialStandardConsumptionDTO.ProcessDTO.MaterialDTO();
                    BigDecimal standardConsumption = BigDecimal.ZERO;
                    if(sfbaTVO.getSfba011().compareTo(BigDecimal.ZERO) > 0) {
                        // 标准消耗
                         standardConsumption =
                                sfbaTVO.getSfba010().divide(sfbaTVO.getSfba011(), 10, BigDecimal.ROUND_HALF_UP);
                    }
                    String place = sfbaTVO.getSfba002();
                    String materialCode = sfbaTVO.getSfba006();
                    log.info("工单号：{} 工序: {}  物料：{} 部位：{} 标准消耗：{}", ticketNo, process, materialCode, place, standardConsumption);
                    materialDTO.setPlace(place);
                    materialDTO.setMaterialCode(materialCode);
                    materialDTO.setStandardConsumption(standardConsumption);
                    materialDTO.setMaterialUnit(sfbaTVO.getSfba014());
                    materialDTO.setMaterialUseType(sfbaTVO.getSfba009());
                    materialDTOS.add(materialDTO);
                });
                processDTO.setMaterialDTOS(materialDTOS);
                processDTOs.add(processDTO);
            });

            apportionmentMaterialStandardConsumptionDTO.setProcessDTOs(processDTOs);
            apportionmentMaterialStandardConsumptionDTOS.put(ticketNo, apportionmentMaterialStandardConsumptionDTO);
        });
        return apportionmentMaterialStandardConsumptionDTOS;
    }

    @RecordMethodTime(msg = "物料使用信息")
    private List<ProductMachineMaterialRecordPO> getProductMachineMaterialRecordPOS(GetMaterialDailySettlementInVO settlementInput, 
                                                                                    StringBuffer sb, List<ApportionmentMaterialDailyDTO> apportionmentMaterialDailyDTOList, 
                                                                                    List<ProductMachineMaterialRecordPO> consumptionRecords) {
        Map<String, BigDecimal> materialConsumptionMap = new HashMap<>();

        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
               .machineName(settlementInput.getMachineName())
               .build();
        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);
        if (isp.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            log.info("=========================按照机位进行分摊==================================");
            // 按照机位进行分摊
            consumptionRecords.forEach(record -> {
                String key = record.getMaterialCode() + "-" + record.getParts();
                BigDecimal newConsumption = record.getConsumptionQuantity();
                materialConsumptionMap.merge(key, newConsumption, BigDecimal::add);
            });
            apportionmentMaterialDailyDTOList.forEach(dto -> {
                // 实际消耗
                BigDecimal bigDecimal = materialConsumptionMap.get(dto.getMaterialCode() + "-" + dto.getParts());
                if (bigDecimal == null) {
                    return;
                }
                sb.append("物料: " + dto.getMaterialCode() + "机位：" + dto.getParts() + "实际消耗：" + bigDecimal + "\n");
                dto.setRealConsumption(bigDecimal.multiply(dto.getConsumptionPercentage()));
                sb.append("物料日分摊结果: " + "机位=" + dto.getParts() + "部位=" + dto.getPlace() + "任务=" + dto.getTaskId() + "工单="
                        + dto.getPlanTicketNo() + "物料=" + dto.getMaterialCode() + "标准消耗=" + dto.getStandardConsumption() + "产量="
                        + dto.getAllYield() + "占比=" + dto.getConsumptionPercentage() + "实际消耗=" + dto.getRealConsumption() + "\n");
            });
            return consumptionRecords;
        } else {
            log.info("=========================按照部位进行分摊==================================");
            // 按照部位进行分摊
            consumptionRecords.forEach(record -> {
                String key = record.getMaterialCode() + "-" + record.getMaterialPlace();
                BigDecimal newConsumption = record.getConsumptionQuantity();
                materialConsumptionMap.merge(key, newConsumption, BigDecimal::add);
            });
            apportionmentMaterialDailyDTOList.forEach(dto -> {
                // 实际消耗
                BigDecimal bigDecimal = materialConsumptionMap.get(dto.getMaterialCode() + "-" + dto.getPlace());
                if (bigDecimal == null) {
                    return;
                }
                dto.setRealConsumption(bigDecimal.multiply(dto.getConsumptionPercentage()));
                sb.append("物料日分摊结果: " + "机位=" + dto.getParts() + "部位=" + dto.getPlace() + "任务=" + dto.getTaskId() + "工单="
                        + dto.getPlanTicketNo() + "物料=" + dto.getMaterialCode() + "标准消耗=" + dto.getStandardConsumption() + "产量="
                        + dto.getAllYield() + "占比=" + dto.getConsumptionPercentage() + "实际消耗=" + dto.getRealConsumption() + "\n");
            });
            return consumptionRecords;
        }


    }

    @RecordMethodTime(msg = "封装消耗信息")
    private List<ApportionmentMaterialDailyDTO> getApportionmentMaterialDailyDTOS(StringBuffer sb, List<GetOneDayTaskSummaryOutVO> taskSummary,
                                                                                  Map<Long, BigDecimal> taskQuantityMap,
                                                                                  Map<String, ApportionmentMaterialStandardConsumptionDTO> ticketMaterialPlacesMap, 
                                                                                  List<ProductMachineMaterialRecordPO> consumptionRecords) {
        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
               .machineName(taskSummary.get(0).getMachineName())
               .build();

        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);

        Map<String, List<ProductMachineMaterialRecordPO>> map =
            consumptionRecords.stream().collect(Collectors.groupingBy(ProductMachineMaterialRecordPO::getMaterialCode));

        List<ApportionmentMaterialDailyDTO> apportionmentMaterialDailyDTOList = Lists.newArrayList();
        taskSummary.forEach(task -> {
            BigDecimal allYield = taskQuantityMap.get(task.getTaskId());
            // 获得任务的机位信息
            List<ProductMachinePartsPO> productMachineParts = productMachinePartsService.getProductMachineParts(task.getTaskId());
            if (productMachineParts.isEmpty()) {
                ProductMachinePartsPO productMachinePartsPO = new ProductMachinePartsPO();
                productMachinePartsPO.setProductTicketId(task.getTaskId());
                productMachinePartsPO.setParts(" ");
                productMachinePartsPO.setPlace(" ");
                productMachineParts.add(productMachinePartsPO);
            }
            productMachineParts.forEach(machinePart -> {
                // 获得该机位的消耗记录
                String parts = machinePart.getParts();
                String place = machinePart.getPlace();
                // 工单的物料的部位的标准消耗
                ApportionmentMaterialStandardConsumptionDTO apportionmentMaterialStandardConsumptionDTO = ticketMaterialPlacesMap.get(task.getPlanTicketNo());
                apportionmentMaterialStandardConsumptionDTO.getProcessDTOs().stream().forEach(app -> {
                    String processCode = app.getProcessCode();
                    app.getMaterialDTOS().stream().forEach(mat -> {
                        // 判断物料是否是这个机位的料
                        List<ProductMachineMaterialRecordPO> productMachineMaterialRecordPOS = map.get(mat.getMaterialCode());
                        if (productMachineMaterialRecordPOS == null) {
                            return;
                        }
                        if (isp.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
                            // 按照机位进行分摊
                            List<String> partsList = productMachineMaterialRecordPOS.stream().map(ProductMachineMaterialRecordPO::getParts).distinct().collect(Collectors.toList());
                            if (!partsList.contains(parts)) {
                                return;
                            }
                        } else {
                            // 按照部位进行分摊
                            List<String> placeList = productMachineMaterialRecordPOS.stream().map(ProductMachineMaterialRecordPO::getMaterialPlace).distinct().collect(Collectors.toList());
                            if (!placeList.contains(place)) {
                                return;
                            }
                        }
                        ApportionmentMaterialDailyDTO apportionmentMaterialDailyDTO = new ApportionmentMaterialDailyDTO();
                        apportionmentMaterialDailyDTO.setParts(parts);
                        apportionmentMaterialDailyDTO.setPlace(place);
                        apportionmentMaterialDailyDTO.setTaskId(task.getTaskId());
                        apportionmentMaterialDailyDTO.setPlanTicketNo(task.getPlanTicketNo());
                        apportionmentMaterialDailyDTO.setProcessCode(processCode);
                        apportionmentMaterialDailyDTO.setAllYield(allYield);
                        apportionmentMaterialDailyDTO.setMaterialCode(mat.getMaterialCode());
                        apportionmentMaterialDailyDTO.setPlace(mat.getPlace());
                        apportionmentMaterialDailyDTO.setStandardConsumption(mat.getStandardConsumption());
                        apportionmentMaterialDailyDTO.setStandardConsumptionYield(mat.getStandardConsumption().multiply(allYield));
                        apportionmentMaterialDailyDTO.setPlanTicketNo(task.getPlanTicketNo());
                        apportionmentMaterialDailyDTO.setProductName(task.getProductName());
                        apportionmentMaterialDailyDTOList.add(apportionmentMaterialDailyDTO);
                    });
                });
            });
        });
        // 计算占比
        // 用于存储每个 materialCode 的 standardConsumptionYield 总和
        Map<String, BigDecimal> totalYieldByMaterialCode = new HashMap<>();
        // 计算每个物料的 standardConsumptionYield 总和
        for (ApportionmentMaterialDailyDTO material : apportionmentMaterialDailyDTOList) {
            String materialCode = material.getMaterialCode();
            BigDecimal yield = material.getStandardConsumptionYield() != null ? material.getStandardConsumptionYield() : BigDecimal.ZERO;
            totalYieldByMaterialCode.merge(materialCode, yield, BigDecimal::add);
        }

        // 计算每个物料的 consumptionPercentage
//        BigDecimal one = BigDecimal.ONE;
        Map<String, BigDecimal> consumptionPercentageMap = new HashMap<>();
        for (int i = 0; i < apportionmentMaterialDailyDTOList.size(); i++) {
            ApportionmentMaterialDailyDTO material = apportionmentMaterialDailyDTOList.get(i);
            String materialCode = material.getMaterialCode();

            BigDecimal yield = material.getStandardConsumptionYield() != null ? material.getStandardConsumptionYield() : BigDecimal.ZERO;
            BigDecimal totalYield = totalYieldByMaterialCode.get(materialCode);

            if (i == apportionmentMaterialDailyDTOList.size() - 1) {
                BigDecimal orDefault = consumptionPercentageMap.getOrDefault(materialCode, BigDecimal.ZERO);
                material.setConsumptionPercentage(BigDecimal.ONE.subtract(orDefault));
                continue;
            }
            if (totalYield != null && totalYield.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal consumptionPercentage = yield.divide(totalYield, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                material.setConsumptionPercentage(consumptionPercentage.divide(BigDecimal.valueOf(100), 4, BigDecimal.ROUND_HALF_UP));
            } else {
                material.setConsumptionPercentage(BigDecimal.ZERO); // 如果总和为0，设为0%
            }
            BigDecimal orDefault = consumptionPercentageMap.getOrDefault(materialCode, BigDecimal.ZERO);
            consumptionPercentageMap.put(materialCode, orDefault.add(material.getConsumptionPercentage()));
        }

        apportionmentMaterialDailyDTOList.forEach(dto -> {
            sb.append("物料日分摊结果: " + "任务=" + dto.getTaskId() + "工序="  + dto.getProcessCode() + "机位=" + dto.getParts() + "部位=" + dto.getPlace() +  "工单="
                + dto.getPlanTicketNo() + "物料=" + dto.getMaterialCode() + "标准消耗=" + dto.getStandardConsumption() + "产量="
                + dto.getAllYield() + "占比=" + dto.getConsumptionPercentage() + "\n");
        });
        return apportionmentMaterialDailyDTOList;
    }

    @RecordMethodTime(msg = "按照物料编码进行分摊")
    private List<GetMaterialDailySettlementOutVO> getGetMaterialDailySettlementOutVOS(List<ApportionmentMaterialDailyDTO> apportionmentMaterialDailyDTOList, List<ProductMachineMaterialRecordPO> consumptionRecords, StringBuffer sb) {
//        物料日分摊结果: 机位= 部位= 任务=8469工单=125-S299-24100000027物料=1010601200006标准消耗=0.0000503281产量=100.0占比=0.4545实际消耗=27.27000
//        物料日分摊结果: 机位= 部位= 任务=8472工单=125-S299-24100000030物料=1010601200006标准消耗=0.0000503281产量=20.0占比=0.0909实际消耗=5.45400
//        物料日分摊结果: 机位= 部位= 任务=8474工单=125-S299-24100000030物料=1010601200006标准消耗=0.0000503281产量=100.0占比=0.4545实际消耗=27.27000
        List<GetMaterialDailySettlementOutVO> dailySettlementList = new ArrayList<>();
        List<ProductMachineMaterialRecordDTO> productMachineMaterialRecordDTOS = BeanUtil.copyToList(consumptionRecords, ProductMachineMaterialRecordDTO.class);

        List<Long> taskIds = apportionmentMaterialDailyDTOList.stream().map(ApportionmentMaterialDailyDTO::getTaskId).distinct().collect(Collectors.toList());
        Map<Long, ProductTicketPO> productTicketPOMap = productTicketService.listByIds(taskIds).stream().collect(Collectors.toMap(ProductTicketPO::getId, Function.identity()));


        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
               .machineName(productMachineMaterialRecordDTOS.get(0).getMachineName())
               .build();
        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);


        // 遍历每一条任务
        apportionmentMaterialDailyDTOList.forEach(dto -> {
            Long taskId = dto.getTaskId();
            sb.append("开始处理任务: "+ taskId + "\n");

            // 任务需要消耗的数量
            dto.setRemainingConsumption(dto.getRealConsumption());
            Iterator<ProductMachineMaterialRecordDTO> iterator = productMachineMaterialRecordDTOS.iterator();
            while (iterator.hasNext()) {
                // 工单不需要再扣料了
                if (dto.getRemainingConsumption() == null || dto.getRemainingConsumption().compareTo(BigDecimal.ZERO) <= 0) {
                    return;
                }
                ProductMachineMaterialRecordDTO consumption = iterator.next();
                if (consumption.getMaterialCode().equals(dto.getMaterialCode())) {
                    sb.append("当前物料"+consumption.getMaterialBarcodeNo()+"与任务物料"+dto.getMaterialCode()+"匹配，开始处理" + "\n");
                    if (isp.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
                        // 按照机位进行分摊
                        if (!consumption.getParts().equals(dto.getParts())) {
                            sb.append("当前物料" + consumption.getMaterialBarcodeNo() + "的机位" + consumption.getParts()
                                + "不属于当前机位" + dto.getParts() + "，跳过" + "\n");
                            continue;
                        }
                    } else {
                        // 按照部位进行分摊
                        if (!consumption.getMaterialPlace().equals(dto.getPlace())) {
                            sb.append("当前物料" + consumption.getMaterialBarcodeNo() + "的部位" + consumption.getMaterialPlace()
                                + "不属于当前部位" + dto.getPlace() + "，跳过" + "\n");
                            continue;
                        }
                    }

                    BigDecimal consumptionQuantity = consumption.getConsumptionQuantity().subtract(consumption.getLastConsumption());
                    if (consumptionQuantity.compareTo(BigDecimal.ZERO) <= 0){
                        return;
                    }
                    // 物料的剩余数量 物料总数量 - 任务的消耗数量
                    BigDecimal deductionQuantity = consumptionQuantity.subtract(dto.getRemainingConsumption());
                    // 当前消耗不够，先扣除全部剩余量，再转到下一项
                    if (deductionQuantity.compareTo(BigDecimal.ZERO) < 0) {
                        GetMaterialDailySettlementOutVO getMaterialDailySettlementOutVO = BeanUtil.copyProperties(consumption, GetMaterialDailySettlementOutVO.class);
                        getMaterialDailySettlementOutVO.setConsumptionQuantity(consumptionQuantity); // 扣完剩余量
                        getMaterialDailySettlementOutVO.setTaskId(taskId);
                        ProductTicketPO productTicketPO = productTicketPOMap.get(taskId);
                        getMaterialDailySettlementOutVO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
                        getMaterialDailySettlementOutVO.setProductName(productTicketPO.getProductName());
                        getMaterialDailySettlementOutVO.setProcess(productTicketPO.getProcess());
                        getMaterialDailySettlementOutVO.setProcessCode(productTicketPO.getProcessCode());
                        dailySettlementList.add(getMaterialDailySettlementOutVO);
                        dto.setRemainingConsumption(deductionQuantity.abs()); // 更新剩余需要扣除的量，转移到下一个消耗列表
                        iterator.remove();
                        sb.append("当前物料"+ consumption.getMaterialBarcodeNo() +"完全消耗完毕" + consumptionQuantity +"，任务剩余需要扣除的量: " + dto.getRemainingConsumption() + "\n");
                        sb.append("当前物料已经被完全扣除，需要扣除下一个物料信息" + "\n");
                    } else {
                        // 当前项足够
                        GetMaterialDailySettlementOutVO getMaterialDailySettlementOutVO = BeanUtil.copyProperties(consumption, GetMaterialDailySettlementOutVO.class);
                        // 剩余需要扣除的量
                        getMaterialDailySettlementOutVO.setConsumptionQuantity(dto.getRemainingConsumption());
                        getMaterialDailySettlementOutVO.setTaskId(taskId);
                        ProductTicketPO productTicketPO = productTicketPOMap.get(taskId);
                        getMaterialDailySettlementOutVO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
                        getMaterialDailySettlementOutVO.setProductName(productTicketPO.getProductName());
                        getMaterialDailySettlementOutVO.setProcess(productTicketPO.getProcess());
                        getMaterialDailySettlementOutVO.setProcessCode(productTicketPO.getProcessCode());

                        dailySettlementList.add(getMaterialDailySettlementOutVO);
                        sb.append("当前物料"+consumption.getMaterialBarcodeNo()+"本次扣除 " +dto.getRemainingConsumption()+"，之前已经扣除的量: " +consumption.getLastConsumption() + "，剩余量: " +
                                consumption.getConsumptionQuantity().subtract(dto.getRemainingConsumption()).subtract(consumption.getLastConsumption()) + "\n");
                        sb.append("当前任务已经扣除完毕，且当前物料已经被部分扣除，下一个任务继续扣除当前物料" + "\n");
                        // 上一个任务已经扣料的数量 修改为当前任务扣除的数量 + 上一个任务已经扣料的数量
                        consumption.setLastConsumption(dto.getRemainingConsumption().add(consumption.getLastConsumption()));
                        // 这个任务已经扣完了，不需要再扣料了
                        dto.setRemainingConsumption(BigDecimal.ZERO);
                    }
                } else {
                    sb.append("当前物料"+consumption.getMaterialBarcodeNo()+"与任务物料"+dto.getMaterialCode()+"不匹配，跳过" + "\n");
                }
            }
        });

        return dailySettlementList;
    }


    @RedisLock
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmMaterialDailyApportionment(String key,GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        ProductMachineDayOutVO productMachineDay = productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO);
        if (Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productMachineDay.getIsApportionment())) {
            throw new CommonException("该日结已经分摊过，请勿重复分摊！");
        }

        // 更新日结信息
        ProductMachineDayDTO productMachineDayDTO = new ProductMachineDayDTO();
        productMachineDayDTO.setMachineName(getMaterialDailySettlementInVO.getMachineName());
        productMachineDayDTO.setProduceDate(getMaterialDailySettlementInVO.getProduceDate());
        productMachineDayDTO.setShift(getMaterialDailySettlementInVO.getShift());
        productMachineDayService.apportionmentProductMachineDay(productMachineDayDTO);

        // 更新分摊信息
        List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOS = Lists.newArrayList();
        List<GetMaterialDailySettlementOutVO> getMaterialDailyApportionmentOutVOS = apportionmentMaterialDaily(getMaterialDailySettlementInVO);
        getMaterialDailyApportionmentOutVOS.forEach(vo -> {
            ProductMachineMaterialApportionmentPO productMachineMaterialApportionmentPO = new ProductMachineMaterialApportionmentPO();
            BeanUtil.copyProperties(vo, productMachineMaterialApportionmentPO);
            productMachineMaterialApportionmentPO.setMachineName(getMaterialDailySettlementInVO.getMachineName());
            productMachineMaterialApportionmentPO.setProduceDate(getMaterialDailySettlementInVO.getProduceDate());
            productMachineMaterialApportionmentPO.setShift(getMaterialDailySettlementInVO.getShift());
            productMachineMaterialApportionmentPO.setPlanTicketNo(vo.getPlanTicketNo());
            productMachineMaterialApportionmentPO.setProcess(vo.getProcess());
            productMachineMaterialApportionmentPO.setProcessCode(vo.getProcessCode());
            productMachineMaterialApportionmentPO.setProductTicketId(vo.getTaskId());
            productMachineMaterialApportionmentPO.setProductName(vo.getProductName());
            productMachineMaterialApportionmentPO.setCompanyCode(SecurityUtil.getCompanySite());
            productMachineMaterialApportionmentPO.setUseType(MaterialUseTypeEnum.APPORTIONMENT_MATERIAL.getIntCode());
            productMachineMaterialApportionmentPOS.add(productMachineMaterialApportionmentPO);
        });
        if (!productMachineMaterialApportionmentPOS.isEmpty()) {
            saveBatch(productMachineMaterialApportionmentPOS);
        }
    }

    @Override
    public List<GetMaterialDailySettlementOutVO> apportionmentUseMaterialInfo(Long id) {
        LambdaQueryWrapper<ProductMachineMaterialApportionmentPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductMachineMaterialApportionmentPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductMachineMaterialApportionmentPO::getProductTicketId, id);
        List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOS = list(lambdaQueryWrapper);
        return BeanUtil.copyToList(productMachineMaterialApportionmentPOS, GetMaterialDailySettlementOutVO.class);
    }

    @Override
    public List<GetMaterialDailySettlementOutVO> pourUseMaterialInfo(Long id) {
        ProductionTicketInfoOutVO productionTicketInfoOutVO = productTicketService.getProductionTicketInfo(id);
        if (Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productionTicketInfoOutVO.getIsPour())) {
            return Lists.newArrayList();
        }
        // 倒扣料校验
        verify(productionTicketInfoOutVO);
        if (productionTicketInfoOutVO.getReportedQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            return Lists.newArrayList();
        }

        List<SfbaTVO> sfbaTVOS = sfbaTService.getByProductTicketNo(productionTicketInfoOutVO.getPlanTicketNo());
        List<ImaalTVO> imaalTList = imaalTService.getImaalTList(sfbaTVOS.stream().map(SfbaTVO::getSfba006).collect(Collectors.toList()));

        Map<String, ImaalTVO> imaalTVOMap = imaalTList.stream().collect(Collectors.toMap(ImaalTVO::getImaal001, Function.identity(), (a, b) -> a));

        // 获得工单的单位
        ReportUnitConvertDTO reportUnitConvertDTO = new ReportUnitConvertDTO();
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productionTicketInfoOutVO.getPlanTicketNo());
        String unit = sfaaTVO.getSfaa013();
        reportUnitConvertDTO.setTargetUnit(unit);
        reportUnitConvertDTO.setPlanTicketNo(productionTicketInfoOutVO.getPlanTicketNo());

//        // 排除已用料的 料号
//        LambdaQueryWrapper<ProductMachineMaterialApportionmentPO> wrapper = Wrappers.lambdaQuery(ProductMachineMaterialApportionmentPO.class)
//                .eq(ProductMachineMaterialApportionmentPO::getCompanyCode, SecurityUtil.getCompanySite())
//                .eq(ProductMachineMaterialApportionmentPO::getProductTicketId, id)
//                .in(ProductMachineMaterialApportionmentPO::getUseType, Arrays.asList(MaterialUseTypeEnum.POSITIVE_MATERIAL.getIntCode(),
//                        MaterialUseTypeEnum.APPORTIONMENT_MATERIAL.getIntCode()));
//        List<String> materialCodes = list(wrapper).stream().map(ProductMachineMaterialApportionmentPO::getMaterialCode).collect(Collectors.toList());
        // 获得物料类型
        List<ImaaTNewDTO> imaaTDTOS = imaaTNewService.getNameByProductNos(sfbaTVOS.stream().filter(sfbaTVO ->
                sfbaTVO.getSfba003().equals(productionTicketInfoOutVO.getProcessCode())).map(SfbaTVO::getSfba006).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(imaaTDTOS)) {
            log.error("工单号：{} 未找到对应的物料类型！不需要倒扣料", productionTicketInfoOutVO.getPlanTicketNo());
            return Lists.newArrayList();
        }
        log.info("查询到的物料类型：{}" + JSON.toJSONString(imaaTDTOS));
        Map<String, String> imaaTMap = imaaTDTOS.stream().collect(Collectors.toMap(ImaaTNewDTO::getImaa001, ImaaTNewDTO::getImaaua004, (a, b) -> a));
        log.info("查询到的物料类型map：{}" + JSON.toJSONString(imaaTMap));

        Set<String> inkTypes = BasePaperConst.getInkTypes();

        List<GetMaterialDailySettlementOutVO> list = sfbaTVOS.stream().filter(sfbaTVO -> sfbaTVO.getSfba003().equals(productionTicketInfoOutVO.getProcessCode()))
                .map(sfbaTVO -> {
                    if (sfbaTVO.getSfba010().compareTo(BigDecimal.ZERO) == 0
                            && sfbaTVO.getSfba011().compareTo(BigDecimal.ZERO) == 0
                    ) {
                        log.info("总应发为0的应该不能判断在内");
                        return null;
                    }
                    String materialType = imaaTMap.get(sfbaTVO.getSfba006());
                    if (!inkTypes.contains(materialType)) {
                        log.info("工单号：{}  物料：{} ，类别：{} 不是油墨或胶水，跳过！", productionTicketInfoOutVO.getPlanTicketNo(), sfbaTVO.getSfba006(), materialType);
                        return null;
                    }

                    // 获得工序的单位
                    EcaaucTPO ecaaucTPO =
                            ecaaucTService.getByCompanyProcess(SecurityUtil.getCompanySite(), sfbaTVO.getSfba003());
                    reportUnitConvertDTO.setSourceUnit(ecaaucTPO.getEcaauc009());
                    reportUnitConvertDTO.setProcessCode(sfbaTVO.getSfba003());
                    GetMaterialDailySettlementOutVO getMaterialDailySettlementOutVO = new GetMaterialDailySettlementOutVO();
                    if (sfbaTVO.getSfba011().equals(BigDecimal.ZERO)) {
                        throw new CommonException("工单：" + productionTicketInfoOutVO.getPlanTicketNo() + " 物料：" + sfbaTVO.getSfba006() + " 标准消耗为0,请在erp中维护标准消耗");
                    }
                    BigDecimal standardConsumption =
                            sfbaTVO.getSfba010().divide(sfbaTVO.getSfba011(), 10, BigDecimal.ROUND_HALF_UP);
                    log.info("工单号：{} 工单类型：{} 工单标准消耗：{}", productionTicketInfoOutVO.getPlanTicketNo(), sfbaTVO.getSfba003(),
                            standardConsumption);
                    BigDecimal allQuantity = productionTicketInfoOutVO.getReportedQuantity()
                            .add(productionTicketInfoOutVO.getUnconfirDefectiveQuantity());
                    reportUnitConvertDTO.setQuantity(allQuantity);
                    BigDecimal execute = reportUnitConvertService.execute(reportUnitConvertDTO);
                    log.info("源单位：{} 目标单位：{} 数量：{} 转换后数量：{}", ecaaucTPO.getEcaauc009(), unit, allQuantity, execute);

                    BigDecimal consumption = standardConsumption.multiply(execute);
                    ImaalTVO imaalTVO = imaalTVOMap.get(sfbaTVO.getSfba006());
                    getMaterialDailySettlementOutVO.setMaterialSeq(sfbaTVO.getSfbaseq() + "");
                    getMaterialDailySettlementOutVO.setMaterialName(imaalTVO.getImaal003());
                    getMaterialDailySettlementOutVO.setMaterialCode(sfbaTVO.getSfba006());
                    getMaterialDailySettlementOutVO.setMaterialPlace(sfbaTVO.getSfba002());
                    getMaterialDailySettlementOutVO.setConsumptionQuantity(consumption);
                    getMaterialDailySettlementOutVO.setPlanTicketNo(productionTicketInfoOutVO.getPlanTicketNo());
                    getMaterialDailySettlementOutVO.setProductName(productionTicketInfoOutVO.getProductName());
                    getMaterialDailySettlementOutVO.setReportedQuantity(productionTicketInfoOutVO.getReportedQuantity());
                    getMaterialDailySettlementOutVO.setDefectiveProduct(productionTicketInfoOutVO.getDefectiveProduct());
                    getMaterialDailySettlementOutVO.setUnconfirDefectiveQuantity(productionTicketInfoOutVO.getUnconfirDefectiveQuantity());
                    getMaterialDailySettlementOutVO.setProcess(productionTicketInfoOutVO.getProcess());
                    getMaterialDailySettlementOutVO.setMaterialUnit(sfbaTVO.getSfba014());
                    getMaterialDailySettlementOutVO.setTaskId(productionTicketInfoOutVO.getId());
                    return getMaterialDailySettlementOutVO;
                }).filter(Objects::nonNull).collect(Collectors.toList());

        // 保留三位小数
        list.stream().forEach(vo -> {
            vo.setConsumptionQuantityP3(vo.getConsumptionQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
            vo.setRemainingQuantityP3(vo.getConsumptionQuantity().setScale(3, BigDecimal.ROUND_HALF_UP));
        });

        return list;
    }

    @RedisLock
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String confirmPourUseMaterialInfo(String key,Long id) {
        ProductionTicketInfoOutVO productionTicketInfoOutVO = productTicketService.getProductionTicketInfo(id);

        String workPlace = null;
        // 安徽工厂需要获得 车间信息
        if (SecurityUtil.getCompanySite().equals(SiteEnum.AHGC.getCode())) {
            ProductionMachineOutVO productionMachineOutVO =
                    productionMachineService.getProductionMachineByErpName(productionTicketInfoOutVO.getMachineName(), productionTicketInfoOutVO.getProcess());
            if (productionMachineOutVO == null) {
                throw new CommonException("未找到"+productionTicketInfoOutVO.getMachineName()+"对应的车间信息，请联系管理员");
            }
            workPlace = productionMachineOutVO.getWorkPlace();
        }

        List<GetMaterialDailySettlementOutVO> materialDailySettlementOutVOS = pourUseMaterialInfo(id);
        if (materialDailySettlementOutVOS.isEmpty()) {
            return "未找到需要倒扣的工单信息！";
        }
        List<ProductMachineMaterialRecordPO> productMachineMaterialRecordPOS = BeanUtil.copyToList(materialDailySettlementOutVOS, ProductMachineMaterialRecordPO.class);
        String finalWorkPlace = workPlace;
        productMachineMaterialRecordPOS.stream().forEach(po -> {
            po.setCompanyCode(SecurityUtil.getCompanySite());
            po.setProductTicketId(id);
            po.setCreateBy(SecurityUtil.getUserId());
            po.setUpdateBy(SecurityUtil.getUserId());
            po.setUseType(MaterialUseTypeEnum.UNDERCUT_MATERIAL.getIntCode());
            po.setMachineName(productionTicketInfoOutVO.getMachineName());
            po.setProduceDate(productionTicketInfoOutVO.getProduceDate());
            po.setShift(productionTicketInfoOutVO.getShift());
            po.setPlanTicketNo(productionTicketInfoOutVO.getPlanTicketNo());
            po.setProcessCode(productionTicketInfoOutVO.getProcessCode());
            po.setStorageNo(finalWorkPlace);
        });
        productMachineMaterialRecordService.saveBatch(productMachineMaterialRecordPOS);

        // 更新日结信息
        ProductTicketPO productTicketPO = productTicketService.getById(id);
        productTicketPO.setIsPour(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        productTicketService.updateById(productTicketPO);

        return "成功倒扣工单信息！";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveMaterialDailyApportionment(ProductMachineMaterialApportionmentPO productMachineMaterialApportionmentPO) {
        productMachineMaterialApportionmentPO.setCompanyCode(SecurityUtil.getCompanySite());
        productMachineMaterialApportionmentPO.setCreateBy(SecurityUtil.getUserId());
        productMachineMaterialApportionmentPO.setUpdateBy(SecurityUtil.getUserId());
        save(productMachineMaterialApportionmentPO);
    }

    @Override
    public List<ProductMachineMaterialApportionmentPO> getByProductTicketId(Long id) {
        LambdaQueryWrapper<ProductMachineMaterialApportionmentPO> lambdaQueryWrapper =
            Wrappers.lambdaQuery(ProductMachineMaterialApportionmentPO.class)
                .eq(ProductMachineMaterialApportionmentPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductMachineMaterialApportionmentPO::getProductTicketId, id);
        return list(lambdaQueryWrapper);
    }

    @Override
    public ProductMachineMaterialApportionmentPO getByMaterialOperationRecordId(Long materialOperationRecordId) {
        LambdaQueryWrapper<ProductMachineMaterialApportionmentPO> lambdaQueryWrapper =
                Wrappers.lambdaQuery(ProductMachineMaterialApportionmentPO.class)
                        .eq(ProductMachineMaterialApportionmentPO::getCompanyCode, SecurityUtil.getCompanySite())
                        .eq(ProductMachineMaterialApportionmentPO::getMaterialOperationId, materialOperationRecordId)
                        .orderByDesc(ProductMachineMaterialApportionmentPO::getCreateTime);
        List<ProductMachineMaterialApportionmentPO> list = list(lambdaQueryWrapper);
        return CollectionUtil.isNotEmpty(list)?list.get(0):null;
    }

    @Override
    public Pagination<GetProductUseMaterialsOutVO> useMaterials(GetProductUseMaterialsInVO getProductUseMaterialsInVO) {
        ProductTicketPO productTicketPO = productTicketService.getById(getProductUseMaterialsInVO.getId());

        GetMaterialDailySettlementInVO getMaterialDailySettlementInVO = new GetMaterialDailySettlementInVO();
        getMaterialDailySettlementInVO.setMachineName(productTicketPO.getMachineName());
        getMaterialDailySettlementInVO.setProduceDate(productTicketPO.getProduceDate());
        getMaterialDailySettlementInVO.setShift(productTicketPO.getShift());
        ProductMachineDayOutVO productMachineDay = productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO);

        // 判断工单是否已扣料到WMS
        boolean isPourToWms = productTicketPO.getIsPourMaterial() != null && 
                              productTicketPO.getIsPourMaterial().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        
        // 如果扣料到wms了，则表中会存  倒扣料的信息
        if (productMachineDay != null && isPourToWms) {
            LambdaQueryWrapper<ProductMachineMaterialApportionmentPO> lambdaQueryWrapper =
                    Wrappers.lambdaQuery(ProductMachineMaterialApportionmentPO.class)
                            .eq(ProductMachineMaterialApportionmentPO::getCompanyCode, SecurityUtil.getCompanySite())
                            .eq(ProductMachineMaterialApportionmentPO::getProductTicketId, getProductUseMaterialsInVO.getId());
            IPage page = page(getProductUseMaterialsInVO.getPage(), lambdaQueryWrapper);
            List<ProductMachineMaterialApportionmentPO> pos = page.getRecords();
            if (CollectionUtils.isEmpty(pos)) {
                return Pagination.newInstance(null);
            }
            
            // 转换为VO并设置表标识和可删除标志
            List<GetProductUseMaterialsOutVO> vos = BeanUtil.copyToList(pos, GetProductUseMaterialsOutVO.class);
            vos.forEach(vo -> {
                vo.setTableType("APPORTIONMENT");
                // 如果已扣料到WMS，则不可删除
                vo.setDeletable(false);
            });
            
            return Pagination.newInstance(vos, page.getTotal(), page.getPages());
        } else {
            // 未扣料到wms，则需要从 两张表数据汇总
            IPage<GetProductUseMaterialsOutVO> page = baseMapper.useMaterials(getProductUseMaterialsInVO.getPage(),getProductUseMaterialsInVO,SecurityUtil.getCompanySite());
            
            // 设置可删除标志 - 未扣料到WMS，且是倒扣料类型的记录才可删除
            if (page != null && page.getRecords() != null) {
                page.getRecords().forEach(vo -> {
                    boolean isUndercutMaterial = MaterialUseTypeEnum.UNDERCUT_MATERIAL.getIntCode().equals(vo.getUseType());
                    vo.setDeletable(isUndercutMaterial && !isPourToWms);
                });
            }
            
            return Pagination.newInstance(page);
        }
    }

    /**
     * 批量删除用料信息，支持同时处理来自两个表的记录
     * @param deleteRecords 要删除的记录列表，包含ID和表类型
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMaterials(List<DeleteMaterialRecordDTO> deleteRecords) {
        if (CollectionUtils.isEmpty(deleteRecords)) {
            return;
        }
        
        // 获取当前公司代码
        String companyCode = SecurityUtil.getCompanySite();
        
        // 分离不同表的ID
        List<Long> apportionmentIds = new ArrayList<>();
        List<Long> materialRecordIds = new ArrayList<>();
        
        for (DeleteMaterialRecordDTO record : deleteRecords) {
            if ("APPORTIONMENT".equals(record.getTableType())) {
                apportionmentIds.add(record.getId());
            } else if ("RECORD".equals(record.getTableType())) {
                materialRecordIds.add(record.getId());
            }
        }
        
        // 处理分摊表中的记录
        List<Long> validApportionmentIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(apportionmentIds)) {
            // 查询分摊表中的记录
            List<ProductMachineMaterialApportionmentPO> apportionmentRecords = this.listByIds(apportionmentIds);
            
            // 过滤出属于当前公司的记录
            List<ProductMachineMaterialApportionmentPO> validApportionmentRecords = apportionmentRecords.stream()
                    .filter(record -> companyCode.equals(record.getCompanyCode()))
                    .collect(Collectors.toList());
            
            for (ProductMachineMaterialApportionmentPO record : validApportionmentRecords) {
                // 只有倒扣料的记录才能删除
                if (!MaterialUseTypeEnum.UNDERCUT_MATERIAL.getIntCode().equals(record.getUseType())) {
                    continue;
                }
                
                // 获取对应的工单信息
                Long productTicketId = record.getProductTicketId();
                if (productTicketId == null) {
                    continue;
                }
                
                ProductTicketPO productTicket = productTicketService.getById(productTicketId);
                if (productTicket == null) {
                    continue;
                }
                
                // 如果已扣料到WMS，则抛出异常
                if (productTicket.getIsPourMaterial() != null && 
                    Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productTicket.getIsPourMaterial())) {
                    throw new CommonException("已扣料到WMS，不能删除相关记录");
                }
                
                validApportionmentIds.add(record.getId());
            }
        }
        
        // 处理物料记录表中的记录
        List<Long> validMaterialRecordIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(materialRecordIds)) {
            // 查询物料记录表中的记录
            List<ProductMachineMaterialRecordPO> materialRecords = productMachineMaterialRecordService.listByIds(materialRecordIds);
            
            // 过滤出属于当前公司且为倒扣料类型的记录
            List<ProductMachineMaterialRecordPO> validMaterialRecords = materialRecords.stream()
                    .filter(record -> companyCode.equals(record.getCompanyCode()) && 
                            MaterialUseTypeEnum.UNDERCUT_MATERIAL.getIntCode().equals(record.getUseType()))
                    .collect(Collectors.toList());
            
            for (ProductMachineMaterialRecordPO record : validMaterialRecords) {
                // 获取对应的工单信息
                Long productTicketId = record.getProductTicketId();
                if (productTicketId == null) {
                    continue;
                }
                
                ProductTicketPO productTicket = productTicketService.getById(productTicketId);
                if (productTicket == null) {
                    continue;
                }
                
                // 如果已扣料到WMS，则抛出异常
                if (productTicket.getIsPourMaterial() != null && 
                    Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productTicket.getIsPourMaterial())) {
                    throw new CommonException("已扣料到WMS，不能删除相关记录");
                }
                
                validMaterialRecordIds.add(record.getId());
            }
        }
        
        // 执行批量删除
        if (!validApportionmentIds.isEmpty()) {
            this.removeByIds(validApportionmentIds);
            log.info("批量删除分摊表用料信息，记录ID：{}", validApportionmentIds);
        }
        
        if (!validMaterialRecordIds.isEmpty()) {
            productMachineMaterialRecordService.removeByIds(validMaterialRecordIds);
            log.info("批量删除物料记录表用料信息，记录ID：{}", validMaterialRecordIds);
        }
        
        if (validApportionmentIds.isEmpty() && validMaterialRecordIds.isEmpty()) {
            log.info("没有符合删除条件的记录");
        }
    }

    private void verify(ProductionTicketInfoOutVO productionTicketInfoOutVO) {
        if (!productionTicketInfoOutVO.getStatus().equals(Integer.valueOf(TicketStatusEnum.COMPLETED.getCode()))) {
            throw new CommonException("任务状态不是已完成，无法进行倒扣，请先下机后触发倒扣料！");
        }
        if (Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productionTicketInfoOutVO.getIsPour())) {
            throw new CommonException("已经倒扣过了，请不要重复倒扣！");
        }
    }

    // ========================================================================================================================================
    // ========================================================================================================================================
    // ========================================================================================================================================
    // ========================================================================================================================================
    // ========================================================================================================================================
    // ========================================================================================================================================
    // ========================================================================================================================================

    // 以下为原有代码，暂时不用，待删除
//    @Override
//    public List<GetMaterialDailyApportionmentOutVO>
//        apportionmentMaterialDaily(GetMaterialDailySettlementInVO settlementInput) {
//        // 日志记录输入参数
//        log.info("开始进行物料日分摊，输入参数: {}", settlementInput);
//        StringBuffer sb = new StringBuffer();
//
//        // 查询汇总后的物料信息
//        ProductMachineMaterialRecordDTO recordQuery = new ProductMachineMaterialRecordDTO();
//        recordQuery.setMachineName(settlementInput.getMachineName());
//        recordQuery.setProduceDate(settlementInput.getProduceDate());
//        recordQuery.setShift(settlementInput.getShift());
//
//        List<ProductMachineMaterialRecordPO> consumptionRecords =
//            productMachineMaterialRecordService.getConsumptionQuantity(recordQuery);
//        sb.append("获取的消耗记录: " + JSON.toJSONString(consumptionRecords) + "\n");
//
//        // 查询机台当天的任务信息汇总
//        List<GetOneDayTaskSummaryOutVO> taskSummary = productTicketService.getOneDayTaskSummary(
//                settlementInput.getMachineName(), settlementInput.getProduceDate(), settlementInput.getShift());
//        sb.append("查询机台当天的任务信息汇总: " + JSON.toJSONString( taskSummary) + "\n");
//
//        // 生产的良品和不良品总数
//        Map<String, BigDecimal> planTicketQuantityMap =
//                taskSummary.stream().collect(Collectors.toMap(GetOneDayTaskSummaryOutVO::getPlanTicketNo,
//                        summary -> summary.getReportedQuantity().add(summary.getDefectiveProduct())));
//        log.info("生产的良品和不良品总数: {}", planTicketQuantityMap);
//
//        // 按物料编号和部位将消耗记录映射
//        Map<String,
//            ProductMachineMaterialRecordPO> materialRecordMap = consumptionRecords.stream()
//                .collect(Collectors.toMap(record -> record.getMaterialCode() + "-" + record.getMaterialPlace(),
//                    Function.identity(), (a, b) -> a));
//
//        // 计算每个物料的实际消耗
//        Map<String, BigDecimal> materialConsumptionMap = new HashMap<>();
//        consumptionRecords.forEach(record -> {
//            String key = record.getMaterialCode() + "-" + record.getMaterialPlace();
//            BigDecimal newConsumption = record.getConsumptionQuantity();
//            materialConsumptionMap.merge(key, newConsumption, BigDecimal::add);
//        });
//        sb.append("计算得到的物料消耗映射:" + JSON.toJSONString( materialConsumptionMap) + "\n");
//
//        List<String> planTicketNos =
//            taskSummary.stream().map(GetOneDayTaskSummaryOutVO::getPlanTicketNo).collect(Collectors.toList());
//        Map<String, List<SfbaTVO>> productTicketNosMap = sfbaTService.getByProductTicketNos(planTicketNos);
//        log.info("获取的工单信息映射: {}", productTicketNosMap);
//
//        // 物料的部位的工单标准消耗
//        Map<String, BigDecimal> materialStandardConsumptionMap =
//            getMaterialPlace(planTicketQuantityMap, productTicketNosMap, settlementInput.getProcessCode());
//        // 每个物料的部位的工单标准消耗占比
//        Map<String, BigDecimal> planTicketMaterialPlaceMap = getMaterialPlaceProportion(planTicketQuantityMap,
//            productTicketNosMap, materialStandardConsumptionMap, settlementInput.getProcessCode());
//
//        // ================================================== 按照物料进行分摊 ============================
//        List<GetMaterialDailyApportionmentOutVO> apportionmentResults = spitByMaterialCode(materialRecordMap,
//            materialConsumptionMap, productTicketNosMap, planTicketMaterialPlaceMap, settlementInput.getProcessCode());
//        // ================================================== 按照条码进行分摊 =============================
//        List<GetMaterialDailyApportionmentOutVO> apportionmentResults2 =
//            spitByBarcode(consumptionRecords, apportionmentResults);
//        // ================================================== 按照任务进行分摊 =============================
//        List<GetMaterialDailyApportionmentOutVO> apportionmentResults3 =
//            spitByTask(settlementInput, apportionmentResults2);
//
//        log.info("工单消耗日志：{}", sb.toString());
//        log.info("物料消耗结果：{}", JSON.toJSONString(apportionmentResults3));
//
//        return apportionmentResults3;
//        // return apportionmentResults2;
//        // return apportionmentResults;
//    }
//
//    private Map<String, BigDecimal> getMaterialPlace(Map<String, BigDecimal> planTicketQuantityMap, Map<String, List<SfbaTVO>> productTicketNosMap, String processCode) {
//        Map<String, BigDecimal> materialStandardConsumptionMap = new HashMap<>();
//        productTicketNosMap.forEach((ticketNo, sfbaTVOs) -> {
//            BigDecimal totalQuantity = planTicketQuantityMap.get(ticketNo);
//            if (totalQuantity.compareTo(BigDecimal.ZERO) == 0) {
//                return;
//            }
//            sfbaTVOs.stream().filter(sfbaTVO -> sfbaTVO.getSfba003().equals(processCode)).forEach(sfbaTVO -> {
//                // 标准消耗
//                BigDecimal standardConsumption = sfbaTVO.getSfba010()
//                    .divide(sfbaTVO.getSfba011(), 10, BigDecimal.ROUND_HALF_UP).multiply(totalQuantity);
//
//                String place = sfbaTVO.getSfba002();
//                String materialCode = sfbaTVO.getSfba006();
//
//                // 更新部位消耗
//                BigDecimal existingConsumption =
//                    materialStandardConsumptionMap.getOrDefault(materialCode + "-" + place, BigDecimal.ZERO);
//                materialStandardConsumptionMap.put(materialCode + "-" + place,
//                    existingConsumption.add(standardConsumption));
//
//                log.info("工单号：{} 物料：{} 部位：{} 标准消耗：{}", ticketNo, materialCode, place, standardConsumption,
//                    totalQuantity);
//                log.info("物料：{} 部位：{} 物料的部位的所有工单标准消耗：{}", materialCode, place,
//                    materialStandardConsumptionMap.get(materialCode + "-" + place));
//            });
//        });
//        return materialStandardConsumptionMap;
//    }
//
//    private Map<String, BigDecimal> getMaterialPlaceProportion(Map<String, BigDecimal> planTicketQuantityMap,
//        Map<String, List<SfbaTVO>> productTicketNosMap, Map<String, BigDecimal> materialStandardConsumptionMap,
//        String processCode) {
//        Map<String, BigDecimal> planTicketMaterialPlaceMap = new HashMap<>();
//        productTicketNosMap.forEach((ticketNo, sfbaTVOs) -> {
//            BigDecimal totalQuantity = planTicketQuantityMap.get(ticketNo);
//            sfbaTVOs.stream().filter(sfbaTVO -> sfbaTVO.getSfba003().equals(processCode)).forEach(sfbaTVO -> {
//                if (totalQuantity.compareTo(BigDecimal.ZERO) == 0) {
//                    return;
//                }
//                // 标准消耗
//                BigDecimal standardConsumption = sfbaTVO.getSfba010()
//                    .divide(sfbaTVO.getSfba011(), 10, BigDecimal.ROUND_HALF_UP).multiply(totalQuantity);
//
//                String place = sfbaTVO.getSfba002();
//                String materialCode = sfbaTVO.getSfba006();
//
//                BigDecimal consumption = materialStandardConsumptionMap.get(materialCode + "-" + place);
//                planTicketMaterialPlaceMap.put(ticketNo + "-" + materialCode + "-" + place,
//                    standardConsumption.divide(consumption, 10, BigDecimal.ROUND_HALF_UP));
//
//                log.info("工单号：{} 物料：{} 部位：{} 标准消耗：{} 所有工单的累计消耗：{} 占比：{}", ticketNo, materialCode, place,
//                    standardConsumption, consumption,
//                    standardConsumption.divide(consumption, 10, BigDecimal.ROUND_HALF_UP));
//            });
//        });
//        return planTicketMaterialPlaceMap;
//    }
//
//    private List<GetMaterialDailyApportionmentOutVO> spitByMaterialCode(Map<String, ProductMachineMaterialRecordPO> materialRecordMap, Map<String, BigDecimal> materialConsumptionMap, Map<String, List<SfbaTVO>> productTicketNosMap, Map<String, BigDecimal> planTicketMaterialPlaceMap, String processCode) {
//        List<GetMaterialDailyApportionmentOutVO> apportionmentResults = new ArrayList<>();
//        Map<String, BigDecimal> actualConsumptionResult = new HashMap<>();
//
//        productTicketNosMap.forEach((ticketNo, sfbaTVOs) -> {
//            GetMaterialDailyApportionmentOutVO apportionmentResult = new GetMaterialDailyApportionmentOutVO();
//            apportionmentResult.setPlanTicketNo(ticketNo);
//
//            sfbaTVOs.stream().filter(sfbaTVO -> sfbaTVO.getSfba003().equals(processCode)).forEach(sfbaTVO -> {
//                BigDecimal standardConsumption =
//                    sfbaTVO.getSfba010().divide(sfbaTVO.getSfba011(), 10, BigDecimal.ROUND_HALF_UP);
//                String place = sfbaTVO.getSfba002();
//                String materialCode = sfbaTVO.getSfba006();
//
//                BigDecimal proportion = planTicketMaterialPlaceMap.get(ticketNo + "-" + materialCode + "-" + place);
//                BigDecimal totalConsumption =
//                    materialConsumptionMap.getOrDefault(materialCode + "-" + place, BigDecimal.ZERO);
//                BigDecimal actualConsumption = totalConsumption.multiply(proportion);
//
//                log.info("工单号：{} 物料：{} 部位：{} 标准消耗：{} 占比：{} 总数：{} 实际消耗：{}", ticketNo, materialCode, place,
//                    standardConsumption, proportion, totalConsumption, actualConsumption);
//
//                actualConsumptionResult.put(ticketNo + "-" + materialCode + "-" + place, standardConsumption);
//
//                // 填充物料消耗信息
//                ProductMachineMaterialRecordPO recordPO = materialRecordMap.get(materialCode + "-" + place);
//                if (recordPO != null) {
//                    GetMaterialDailySettlementOutVO settlementInfo =
//                        BeanUtil.copyProperties(recordPO, GetMaterialDailySettlementOutVO.class);
//                    settlementInfo.setConsumptionQuantity(actualConsumption);
//                    settlementInfo.setMaterialBarcodeNo(null);
//                    apportionmentResult.getDailySettlementList().add(settlementInfo);
//                }
//            });
//
//            if (!apportionmentResult.getDailySettlementList().isEmpty()) {
//                apportionmentResults.add(apportionmentResult);
//            }
//        });
//        return apportionmentResults;
//    }
//
//    private List<GetMaterialDailyApportionmentOutVO> spitByBarcode(List<ProductMachineMaterialRecordPO> consumptionRecords, List<GetMaterialDailyApportionmentOutVO> apportionmentResults) {
//        List<GetMaterialDailyApportionmentOutVO> apportionmentResults2 = new ArrayList<>();
//        // 剩余的物料消耗
//        BigDecimal[] lastConsumption = {BigDecimal.ZERO};
//        // 按照物料编码进行分摊
//        apportionmentResults.forEach(apportionment -> {
//            String ticketNo = apportionment.getPlanTicketNo();
//            List<ProductMachineMaterialRecordPO> deductionDetails = new ArrayList<>();
//
//            apportionment.getDailySettlementList().forEach(settlement -> {
//                BigDecimal[] remainingConsumption = {settlement.getConsumptionQuantity()};
//                Iterator<ProductMachineMaterialRecordPO> iterator = consumptionRecords.iterator();
//                while (iterator.hasNext()) {
//                    // 工单不需要再扣料了
//                    if (remainingConsumption[0].compareTo(BigDecimal.ZERO) <= 0) {
//                        return;
//                    }
//                    ProductMachineMaterialRecordPO consumption = iterator.next();
//                    if (consumption.getMaterialCode().equals(settlement.getMaterialCode())
//                        && consumption.getMaterialPlace().equals(settlement.getMaterialPlace())) {
//
//                        BigDecimal consumptionQuantity =
//                            consumption.getConsumptionQuantity().subtract(lastConsumption[0]);
//                        // 物料的剩余数量 物料总数量 - 任务的消耗数量
//                        BigDecimal deductionQuantity = consumptionQuantity.subtract(remainingConsumption[0]);
//                        // 当前消耗不够，先扣除全部剩余量，再转到下一项
//                        if (deductionQuantity.compareTo(BigDecimal.ZERO) < 0) {
//                            ProductMachineMaterialRecordPO deductionRecord = new ProductMachineMaterialRecordPO();
//                            BeanUtil.copyProperties(consumption, deductionRecord);
//                            deductionRecord.setConsumptionQuantity(consumptionQuantity); // 扣完剩余量
//                            if (remainingConsumption[0].compareTo(BigDecimal.ZERO) > 0) {
//                                deductionDetails.add(deductionRecord);
//                                lastConsumption[0] = BigDecimal.ZERO;
//                                iterator.remove();
//                            }
//                            remainingConsumption[0] = deductionQuantity.abs(); // 更新剩余量，转移到下一个消耗列表
//                        } else {
//                            // 当前项足够
//                            ProductMachineMaterialRecordPO deductionRecord = new ProductMachineMaterialRecordPO();
//                            BeanUtil.copyProperties(consumption, deductionRecord);
//                            deductionRecord.setConsumptionQuantity(remainingConsumption[0]);
//                            if (consumptionQuantity.compareTo(BigDecimal.ZERO) > 0) {
//                                deductionDetails.add(deductionRecord);
//                                settlement.setConsumptionQuantity(deductionQuantity);
//                                lastConsumption[0] = remainingConsumption[0];
//                                remainingConsumption[0] = BigDecimal.ZERO;
//                            }
//                        }
//
//                    }
//                }
//            });
//
//            log.info("工单号：{} 扣减结果：{}", ticketNo, JSON.toJSONString(deductionDetails));
//            GetMaterialDailyApportionmentOutVO getMaterialDailyApportionmentOutVO =
//                new GetMaterialDailyApportionmentOutVO();
//            getMaterialDailyApportionmentOutVO.setPlanTicketNo(ticketNo);
//            getMaterialDailyApportionmentOutVO
//                .setDailySettlementList(BeanUtil.copyToList(deductionDetails, GetMaterialDailySettlementOutVO.class));
//            apportionmentResults2.add(getMaterialDailyApportionmentOutVO);
//        });
//        return apportionmentResults2;
//    }
//
//    private List<GetMaterialDailyApportionmentOutVO> spitByTask(GetMaterialDailySettlementInVO settlementInput, List<GetMaterialDailyApportionmentOutVO> apportionmentResults2) {
//        List<GetOneDayTaskSummaryOutVO> oneDayTaskSummaryDetail = productTicketService.getOneDayTaskSummaryDetail(
//            settlementInput.getMachineName(), settlementInput.getProduceDate(), settlementInput.getShift());
//        // 再按照任务id进行分摊
//        List<GetMaterialDailyApportionmentOutVO> apportionmentResults3 = new ArrayList<>();
//        apportionmentResults2.forEach(apportionment -> {
//            String ticketNo = apportionment.getPlanTicketNo();
//            List<GetMaterialDailySettlementOutVO> deductionDetails = new ArrayList<>();
//
//            // 按照物料编码进行分组
//            Map<String, List<GetMaterialDailySettlementOutVO>> materialCodeMap = apportionment.getDailySettlementList().stream()
//                .collect(Collectors.groupingBy(a -> a.getMaterialCode() + "-" + a.getMaterialPlace()));
//
//            materialCodeMap.entrySet().forEach(entry -> {
//                List<GetMaterialDailySettlementOutVO> materialDailySettlementOutVOS = entry.getValue();
//                // 工单的总消耗量
//                BigDecimal sumConsumption = materialDailySettlementOutVOS.stream()
//                        .map(GetMaterialDailySettlementOutVO::getConsumptionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//                if (sumConsumption.compareTo(BigDecimal.ZERO) <= 0) {
//                    return;
//                }
//
//                // 遍历任务信息
//                for (GetOneDayTaskSummaryOutVO consumption : oneDayTaskSummaryDetail) {
//                    BigDecimal taskConsumptionQuantity = consumption.getProductRatio().multiply(sumConsumption); // 任务的消耗数量
//                    BigDecimal hasConsumptionQuantity = BigDecimal.ZERO; // 当前任务的已扣除量
//
//                    Iterator<GetMaterialDailySettlementOutVO> dailySettlementOutVOIterator =
//                            materialDailySettlementOutVOS.iterator();
//                    while (dailySettlementOutVOIterator.hasNext()
//                            && hasConsumptionQuantity.compareTo(taskConsumptionQuantity) < 0) { // 剩下的消耗量小于任务消耗量
//                        GetMaterialDailySettlementOutVO settlement = dailySettlementOutVOIterator.next();
//                        BigDecimal remainingConsumption = settlement.getConsumptionQuantity(); // 初始化剩余消耗量
//
//                        if (consumption.getPlanTicketNo().equals(ticketNo)) {
//                            // 计算任务还需要扣除的量
//                            BigDecimal consumptionNeeded = taskConsumptionQuantity.subtract(hasConsumptionQuantity);
//                            BigDecimal deductionQuantity = remainingConsumption.subtract(consumptionNeeded);  // 扣除后剩余的量
//
//                            GetMaterialDailySettlementOutVO deductionRecord = new GetMaterialDailySettlementOutVO();
//                            BeanUtil.copyProperties(settlement, deductionRecord);
//                            deductionRecord.setTaskId(consumption.getTaskId());
//
//                            if (deductionQuantity.compareTo(BigDecimal.ZERO) < 0) {
//                                // 当前物料不足，扣除全部剩余量
//                                deductionRecord.setConsumptionQuantity(remainingConsumption);
//                                hasConsumptionQuantity = hasConsumptionQuantity.add(remainingConsumption); // 更新已扣除量
//                                deductionDetails.add(deductionRecord);
//                                dailySettlementOutVOIterator.remove(); // 消耗完毕，移除该物料项
//                            } else {
//                                // 当前物料足够，扣除需求量
//                                deductionRecord.setConsumptionQuantity(consumptionNeeded);
//                                hasConsumptionQuantity = hasConsumptionQuantity.add(consumptionNeeded); // 更新已扣除量
//                                deductionDetails.add(deductionRecord);
//                                settlement.setConsumptionQuantity(deductionQuantity); // 更新当前物料剩余量
//                            }
//                        }
//                    }
//                }
//            });
//
//            log.info("工单号：{} 扣减结果：{}", ticketNo, JSON.toJSONString(deductionDetails));
//            GetMaterialDailyApportionmentOutVO getMaterialDailyApportionmentOutVO =
//                new GetMaterialDailyApportionmentOutVO();
//            getMaterialDailyApportionmentOutVO.setPlanTicketNo(ticketNo);
//            getMaterialDailyApportionmentOutVO
//                .setDailySettlementList(BeanUtil.copyToList(deductionDetails, GetMaterialDailySettlementOutVO.class));
//            apportionmentResults3.add(getMaterialDailyApportionmentOutVO);
//        });
//        return apportionmentResults3;
//    }

}
