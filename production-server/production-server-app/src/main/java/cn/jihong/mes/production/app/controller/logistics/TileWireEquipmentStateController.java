package cn.jihong.mes.production.app.controller.logistics;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.jihong.logistics.api.model.vo.in.TileWireEquipmentStatePageInVO;
import cn.jihong.logistics.api.model.vo.out.TileWireEquipmentStateOutVO;
import cn.jihong.logistics.api.service.ITileWireEquipmentStateService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;

/**
 * 瓦线设备运行数据
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@RestController
@RequestMapping("/tileWireEquipmentState")
@ShenyuSpringMvcClient(path = "/tileWireEquipmentState/**")
public class TileWireEquipmentStateController {

    @DubboReference
    private ITileWireEquipmentStateService service;

    /**
     * 获取详情
     * @param id
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.TileWireEquipmentStateOutVO>
     */
    @GetMapping("/getSingleTileWireEquipmentStateById/{id}")
    public StandardResult<TileWireEquipmentStateOutVO> getSingleTileWireEquipmentStateById(@PathVariable("id") Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getSingleTileWireEquipmentStateById(id));
    }

    /**
     * 获取分页列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.TileWireEquipmentStateOutVO>>
     */
    @PostMapping("/getTileWireEquipmentStatePage")
    public StandardResult<Pagination<TileWireEquipmentStateOutVO>> getTileWireEquipmentStatePage(@RequestBody @Valid TileWireEquipmentStatePageInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getTileWireEquipmentStatePage(inVO));
    }


}

