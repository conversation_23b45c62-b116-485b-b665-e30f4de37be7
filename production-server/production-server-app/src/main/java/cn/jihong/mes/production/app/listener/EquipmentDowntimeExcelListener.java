package cn.jihong.mes.production.app.listener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.extra.spring.SpringUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeInVO;
import cn.jihong.mes.production.api.service.IEquipmentDowntimeProcessService;
import cn.jihong.mes.production.api.service.IEquipmentDowntimeService;
import cn.jihong.oa.erp.api.model.vo.GetOocqlTByProcessNameOutVO;
import cn.jihong.oa.erp.api.service.IOocqlTService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;


import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024-08-07 17:24
 */
@Component
@Slf4j
public class EquipmentDowntimeExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    private IOocqlTService iOocqlTService;

    private List<EquipmentDowntimeInVO> list = new ArrayList<>();

    public EquipmentDowntimeExcelListener(IOocqlTService iOocqlTService){
        this.iOocqlTService = iOocqlTService;
    }


    @Override
    public void invokeHeadMap(Map headMap, AnalysisContext context) {
        log.info("内容表头数据: {}", headMap);
    }

    @Override
    public void invoke(Map<Integer, String> dataMap, AnalysisContext context) {
        log.info("解析到一条数据:{}",  JSONUtil.toJsonStr(dataMap));
        EquipmentDowntimeInVO downtimeInVO = new EquipmentDowntimeInVO();
        downtimeInVO.setCompanyCode(SecurityUtil.getCompanySite());
        downtimeInVO.setDowntimeCode(dataMap.get(0));
        downtimeInVO.setLossType(dataMap.get(1));
        downtimeInVO.setCodeType(Integer.parseInt(dataMap.get(2)));
        downtimeInVO.setDowntimeReason(dataMap.get(3));
        if(downtimeInVO.getCodeType() == 2){
            if(StringUtils.isBlank(dataMap.get(4))) {
                throw  new CommonException("专用工序名称不能为空!");
            }
            downtimeInVO.setProcessName(dataMap.get(4));
        }
        list.add(downtimeInVO);
    }



    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("内容所有数据解析完成！");
        List<String> processNameList = list.stream().map(EquipmentDowntimeInVO::getProcessName).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        Map<String, String> map = new HashMap<>();
        if(CollectionUtil.isNotEmpty(processNameList)) {
            List<GetOocqlTByProcessNameOutVO> processNameOutVOList = iOocqlTService.getOocqlTByProcessName(SecurityUtil.getCompanySite(), processNameList);
            map = processNameOutVOList.stream().collect(Collectors.toMap(GetOocqlTByProcessNameOutVO::getOocql004, GetOocqlTByProcessNameOutVO::getOocq002));
        }
        log.info("停机代码erp有的专用工序：{}",JSONUtil.toJsonStr(map));
        IEquipmentDowntimeService iEquipmentDowntimeService = SpringUtil.getBean("equipmentDowntimeServiceImpl");
        Map<String, String> finalMap = map;
        list.forEach(equipmentDowntimeInVO -> {
            try {
                equipmentDowntimeInVO.setProcessCode(finalMap.get(equipmentDowntimeInVO.getProcessName()));
                iEquipmentDowntimeService.saveEquipmentDowntime(equipmentDowntimeInVO);
            }catch (Exception e){
                log.error("停机代码导入-专用工序：{},:{}",equipmentDowntimeInVO.getProcessName(),e.getMessage());
            }
        });
        list.clear();
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("内容解析失败：{}",exception.getMessage());
        exception.printStackTrace();
    }

}
