package cn.jihong.mes.production.app.service.machineTask;

import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.enums.MachineTaskTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import cn.jihong.mes.production.api.model.vo.in.GetMachineTaskByNameInVO;
import cn.jihong.mes.production.api.model.vo.in.SaveMachineTaskInVO;
import cn.jihong.mes.production.api.model.vo.out.GetMachineTaskByNameOutVO;
import cn.jihong.mes.production.api.service.IProductMachineTaskHistoryService;
import cn.jihong.mes.production.api.service.IProductMachineTaskService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 转单换版
 * <AUTHOR>
 * @date 2024-09-05 13:42
 */
@Component
public class ChangeVersionMachineTaskImpl extends AbstractMachineTaskAction {

    @Resource
    private IProductMachineTaskService iProductMachineTaskService;

    @Resource
    private IProductMachineTaskHistoryService iProductMachineTaskHistoryService;

    @Resource
    private IProductTicketService iProductTicketService;

    @Override
    public Integer getMachineTaskCode(){
       return MachineTaskTypeEnum.TRANSFER_OF_ORDER.getCode();
    }


    @Override
    public Boolean execute(SaveMachineTaskInVO inVO) {
        AssertUtil.isNotNull(inVO.getReportedProductId(),"尚未创建工单，无法换版");

        List<ProductMachineTaskPO> changeVersionList = iProductMachineTaskService.getByProductTicketId(inVO.getReportedProductId(), inVO.getType());
        AssertUtil.isEmpty(changeVersionList,"当前工单已换版过");

        AssertUtil.isNotBlank(inVO.getChangeVersionType(),"换版类型不能为空");
        AssertUtil.isNotBlank(inVO.getChangeVersionNewOld(),"新旧版不能为空");
        AssertUtil.isNotNull(inVO.getChangeVersionQuantity(),"换版数量不能为空");

        GetMachineTaskByNameInVO getMachineTaskByNameInVO = new GetMachineTaskByNameInVO();
        getMachineTaskByNameInVO.setMachineName(inVO.getMachineName());
        GetMachineTaskByNameOutVO machineTaskByName = iProductMachineTaskService.getMachineTaskByName(getMachineTaskByNameInVO);
        if(Objects.nonNull(machineTaskByName)){

            AssertUtil.isTrue(machineTaskByName.getReportedProductId().equals(inVO.getReportedProductId()),"机台任务工单与当前工单不一致，请重扫机台码");
            if(machineTaskByName.getType().equals(inVO.getType())){
                // 当前的状态已经是 转单换版 不用结束再开始
                ProductMachineTaskPO productMachineTaskPO = iProductMachineTaskService.getById(machineTaskByName.getId());
                productMachineTaskPO.setChangeVersionType(inVO.getChangeVersionType());
                productMachineTaskPO.setChangeVersionNewOld(inVO.getChangeVersionNewOld());
                productMachineTaskPO.setChangeVersionQuantity(inVO.getChangeVersionQuantity());
                productMachineTaskPO.setUpdateTime(new Date());
                productMachineTaskPO.setUpdateBy(SecurityUtil.getUserId());
                iProductMachineTaskService.updateById(productMachineTaskPO);

                return iProductMachineTaskHistoryService.saveMachineTaskHistory(productMachineTaskPO, null,null,null);
            }

            iProductMachineTaskService.stopMachineTaskById(machineTaskByName.getId());
        }

       return startMachineTask(inVO);
    }

    /**
     * 开始转单/换版 机台任务
     * @param inVO
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024-09-08 16:14
     */
    public Boolean startMachineTask(SaveMachineTaskInVO inVO) {
        ProductMachineTaskPO productMachineTaskPO = new ProductMachineTaskPO();
        productMachineTaskPO.setMachineName(inVO.getMachineName());
        productMachineTaskPO.setType(inVO.getType());
        productMachineTaskPO.setReportedProductId(inVO.getReportedProductId());
        productMachineTaskPO.setChangeVersionType(inVO.getChangeVersionType());
        productMachineTaskPO.setChangeVersionNewOld(inVO.getChangeVersionNewOld());
        productMachineTaskPO.setChangeVersionQuantity(inVO.getChangeVersionQuantity());
        productMachineTaskPO.setFinished(BooleanEnum.FALSE.getCode());
        productMachineTaskPO.setStartTime(new Date());
        productMachineTaskPO.setCreateBy(SecurityUtil.getUserId());
        iProductMachineTaskService.save(productMachineTaskPO);
        return iProductMachineTaskHistoryService.saveMachineTaskHistory(productMachineTaskPO, null,null,null);
    }
}
