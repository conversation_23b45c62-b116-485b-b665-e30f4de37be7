package cn.jihong.mes.production.app.controller.logistics;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.logistics.api.model.vo.in.TileWireEquipmentDataPageInVO;

import cn.jihong.logistics.api.model.vo.out.TileWireEquipmentDataOutVO;
import cn.jihong.logistics.api.service.ITileWireEquipmentDataService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 瓦线设备数据
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@RestController
@RequestMapping("/tileWireEquipmentData")
@ShenyuSpringMvcClient(path = "/tileWireEquipmentData/**")
public class TileWireEquipmentDataController {

    @DubboReference
    private ITileWireEquipmentDataService service;

    /**
     * 获取详情
     * @param id
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.mes.production.api.model.vo.out.TileWireEquipmentDataOutVO>
     */
    @GetMapping("/getSingleTileWireEquipmentDataById/{id}")
    public StandardResult<TileWireEquipmentDataOutVO> getSingleTileWireEquipmentDataById(@PathVariable("id") Long id){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getSingleTileWireEquipmentDataById(id));
    }

    /**
     * 获取分页列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<Pagination<cn.jihong.mes.production.api.model.vo.out.TileWireEquipmentDataOutVO>>
     */
    @PostMapping("/getTileWireEquipmentDataPage")
    public StandardResult<Pagination<TileWireEquipmentDataOutVO>> getTileWireEquipmentDataPage(@RequestBody @Valid TileWireEquipmentDataPageInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,service.getTileWireEquipmentDataPage(inVO));
    }


}

