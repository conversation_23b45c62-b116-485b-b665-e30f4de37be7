package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.convertor.PageConvertor;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.EquipmentDowntimeProcessPO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimeProcessInVO;
import cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeProcessOutVO;
import cn.jihong.mes.production.app.mapper.EquipmentDowntimeProcessMapper;
import cn.jihong.mes.production.api.service.IEquipmentDowntimeProcessService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 设备停机工序关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@DubboService
public class EquipmentDowntimeProcessServiceImpl extends JiHongServiceImpl<EquipmentDowntimeProcessMapper, EquipmentDowntimeProcessPO> implements IEquipmentDowntimeProcessService {

    @Override
    public Boolean saveEquipmentDowntimeProcess(EquipmentDowntimeProcessInVO inVO){
        EquipmentDowntimeProcessPO po = new EquipmentDowntimeProcessPO();
        BeanUtil.copyProperties(inVO,po);
        po.setCreateBy(SecurityUtil.getUserId());
        po.setCreateTime(LocalDateTime.now());
        return save(po);
    }

    @Override
    public List<EquipmentDowntimeProcessPO> getListByEquipmentDowntimeId(Long equipmentDowntimeId) {
        LambdaQueryWrapper<EquipmentDowntimeProcessPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(EquipmentDowntimeProcessPO::getEquipmentDowntimeId,equipmentDowntimeId);
        return list(wrapper);
    }

    @Override
    public Boolean deleteByIds(String ids){
        if (StringUtils.isBlank(ids)) {
            throw new CommonException("删除条件id不能为空");
        }
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::parseLong).collect(Collectors.toList());
        return removeByIds(idList);
    }


    @Override
    public EquipmentDowntimeProcessOutVO getSingleEquipmentDowntimeProcessById(Long id){
        if (ObjectUtils.isNull(id) || id <= 0L) {
            throw new CommonException("id不能为空！");
        }
        return BeanUtil.copyProperties(getById(id),EquipmentDowntimeProcessOutVO.class);
    }

}
