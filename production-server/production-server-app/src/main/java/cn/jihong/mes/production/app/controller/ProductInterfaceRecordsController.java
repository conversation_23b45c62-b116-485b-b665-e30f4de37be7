package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.GetRecordsByMainInfoInVO;
import cn.jihong.mes.production.api.model.vo.in.GetStorageApplyInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.GetRecordsByMainInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetStorageApplyInfoOutVO;
import cn.jihong.mes.production.api.service.IProductInterfaceRecordsService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 接口调用记录
 *
 * <AUTHOR>
 * @since 2024-04-17
 */
@RestController
@RequestMapping("/productInterfaceRecords")
@ShenyuSpringMvcClient(path = "/productInterfaceRecords/**")
public class ProductInterfaceRecordsController {

    @Autowired
    private IProductInterfaceRecordsService productInterfaceRecordsService;

    /**
     * 接口调用记录查询
     */
    @PostMapping("/getRecordsByMainInfo")
    public StandardResult<Pagination<GetRecordsByMainInfoOutVO>> getRecordsByMainInfo(@RequestBody @Valid GetRecordsByMainInfoInVO getRecordsByMainInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInterfaceRecordsService.getRecordsByMainInfo(getRecordsByMainInfoInVO));
    }

    /**
     * 入库申请信息查询
     */
    @PostMapping("/getStorageApplyInfo")
    public StandardResult<Pagination<GetStorageApplyInfoOutVO>> getStorageApplyInfo(@RequestBody @Valid GetStorageApplyInfoInVO getStorageApplyInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productInterfaceRecordsService.getStorageApplyInfo(getStorageApplyInfoInVO));
    }


}

