package cn.jihong.mes.production.app.field.mapper;

import cn.jihong.oa.approve.api.enums.BusinessType;
import cn.jihong.workflow.common.biz.IFieldNameMapper;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class OutProcessingFieldNameMapper implements IFieldNameMapper {

    private static final Map<String , Map<String, String>> TABLE_FIELD_MAP = Maps.newHashMap();
    static {
        Map<String, String> mainTableFieldName = Maps.newHashMap();
        mainTableFieldName.put("dh", "requestNo");
        mainTableFieldName.put("sqrq", "applyDate");
        mainTableFieldName.put("sqr", "applicantUserId");
        mainTableFieldName.put("ssbm", "department");
        mainTableFieldName.put("jtbm", "jtbm");
        mainTableFieldName.put("lfyq", "lfyq");
        mainTableFieldName.put("fjsc", "attachmentList");
        mainTableFieldName.put("xglc", "xglc");
        mainTableFieldName.put("xgwd", "xgwd");
        mainTableFieldName.put("manager", "manager");
        mainTableFieldName.put("flag", "flag");

        mainTableFieldName.put("cpmc", "productName");
        mainTableFieldName.put("gzdh", "workOrderNumber");
        mainTableFieldName.put("jggy", "processingTechnology");
        mainTableFieldName.put("wfsl", "externalQuantity");
        mainTableFieldName.put("qwwcsj", "expectsCompletionTime");
        mainTableFieldName.put("jgyqjpgqr", "processingRequirements");
        mainTableFieldName.put("sfdb", "supervise");
        mainTableFieldName.put("dbr", "supervisor");
        mainTableFieldName.put("gsbm", "belongingDepartment");
        mainTableFieldName.put("gsgs", "belongingCompany");
        mainTableFieldName.put("lfeq", "lfeq");
        mainTableFieldName.put("xmbzgy", "xmbzgy");
        mainTableFieldName.put("jggys", "processingSupplier");
        mainTableFieldName.put("zjgf", "totalProcessingFee");
        mainTableFieldName.put("jgdj", "processingUnitPrice");
        mainTableFieldName.put("gdh", "workOrderId");
        mainTableFieldName.put("gxmc", "processName");
        mainTableFieldName.put("gys", "supplier");

        mainTableFieldName.put("cpgg", "productSpecification");
        mainTableFieldName.put("ERPjd", "ERPjd");
        mainTableFieldName.put("hsold", "hsold");
        mainTableFieldName.put("hsnew", "hsnew");
        mainTableFieldName.put("gyslx", "supplierType");


        TABLE_FIELD_MAP.put(MAIN_TABLE_NAME, mainTableFieldName);


    }

    @Override
    public Long getWorkflowId() {

        return BusinessType.OUTSIDE_PROCESSING.getWorkflowid();
    }

    @Override
    public Map<String, Map<String, String>> getTablesFieldMap() {
        return TABLE_FIELD_MAP;
    }
}
