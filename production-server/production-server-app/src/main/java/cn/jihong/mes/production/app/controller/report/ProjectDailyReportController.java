package cn.jihong.mes.production.app.controller.report;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.report.IProjectDailyReportServer;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 工程单日报
 * <AUTHOR>
 * @date 2024/3/8 11:13
 */
@RestController
@RequestMapping("/projectDailyReport/")
@ShenyuSpringMvcClient(path = "/projectDailyReport/**")
public class ProjectDailyReportController {

    @Resource
    private IProjectDailyReportServer iProjectDailyReportServer;

    /**
     * 工程单日报表（旧的）
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.common.model.Pagination < cn.jihong.mes.production.api.model.vo.out.GetProjectDailyReportsOutVO>>
     * <AUTHOR>
     * @date: 2024/3/8 11:30
     */
    @PostMapping("/getProjectDailyReports")
    public StandardResult<Pagination<GetProjectDailyReportsOutVO>> getProjectDailyReports(@RequestBody @Valid GetProjectDailyReportsInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProjectDailyReportServer.getProjectDailyReports(inVO));
    }

    /**
     * 工程单日报表-日内
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.common.model.Pagination < cn.jihong.mes.production.api.model.vo.out.GetPageByDayOutVO>>
     * <AUTHOR>
     * @date: 2024/3/8 11:30
     */
    @PostMapping("/getPageByDay")
    public StandardResult<Pagination<GetPageByDayOutVO>> getPageByDay(@RequestBody @Valid GetPageByDayInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProjectDailyReportServer.getPageByDay(inVO));
    }

    /**
     * 工程单日报表-日内详情
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.common.model.Pagination < cn.jihong.mes.production.api.model.vo.out.GetPageByDayDetailOutVO>>
     * <AUTHOR>
     * @date: 2024/3/8 11:30
     */
    @PostMapping("/getPageByDayDetail")
    public StandardResult<Pagination<GetPageByDayDetailOutVO>> getPageByDayDetail(@RequestBody @Valid GetPageByDayDetailInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProjectDailyReportServer.getPageByDayDetail(inVO));
    }


    /**
     * 工程单日报表-截止
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.common.model.Pagination < cn.jihong.mes.production.api.model.vo.out.GetProjectDailyReportsOutVO>>
     * <AUTHOR>
     * @date: 2024/3/8 11:30
     */
    @PostMapping("/getPageByEnd")
    public StandardResult<Pagination<GetPageByEndOutVO>> getPageByEnd(@RequestBody @Valid GetPageByEndInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProjectDailyReportServer.getPageByEnd(inVO));
    }

    /**
     * 工程单日报表-截止详情
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult<cn.jihong.common.model.Pagination<cn.jihong.mes.production.api.model.vo.out.GetPageByEndDetailOutVO>>
     * <AUTHOR>
     * @date: 2024/4/23 11:11
     */
    @PostMapping("/getPageByEndDetail")
    public StandardResult<Pagination<GetPageByEndDetailOutVO>> getPageByEndDetail(@RequestBody @Valid GetPageByEndDetailInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, iProjectDailyReportServer.getPageByEndDetail(inVO));
    }
}
