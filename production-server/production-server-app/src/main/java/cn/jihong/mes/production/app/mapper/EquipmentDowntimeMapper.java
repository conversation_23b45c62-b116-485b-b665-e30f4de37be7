package cn.jihong.mes.production.app.mapper;

import cn.jihong.mes.production.api.model.po.EquipmentDowntimePO;
import cn.jihong.mes.production.api.model.vo.in.EquipmentDowntimePageInVO;
import cn.jihong.mes.production.api.model.vo.in.GetMaterialListPageInVO;
import cn.jihong.mes.production.api.model.vo.in.GetOeeInVO;
import cn.jihong.mes.production.api.model.vo.out.EquipmentDowntimeOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetOeeEquipmentDowntime;
import cn.jihong.mes.production.api.model.vo.out.MaterialUseOutVO;
import cn.jihong.mybatis.api.mapper.JiHongMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备停机代码表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface EquipmentDowntimeMapper extends JiHongMapper<EquipmentDowntimePO> {

    Page<EquipmentDowntimeOutVO> getEquipmentDowntimePage(IPage page, @Param("inVO") EquipmentDowntimePageInVO inVO);

    List<GetOeeEquipmentDowntime> getOeeEquipmentDowntimeList(@Param("inVO") GetOeeInVO inVO);

}
