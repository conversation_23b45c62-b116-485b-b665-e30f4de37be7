package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductSettlementEndProductionPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductDefectiveProductsService;
import cn.jihong.mes.production.api.service.IProductLastPalletService;
import cn.jihong.mes.production.api.service.IProductSettlementEndProductionService;
import cn.jihong.mes.production.api.service.IProductTicketService;
import cn.jihong.mes.production.app.mapper.ProductDefectiveProductsMapper;
import cn.jihong.mes.production.app.mapper.ProductSettlementEndProductionMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.vo.GetProcessSeqByTickNoOutVO;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import cn.jihong.tms.api.model.enums.TicketStatusEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 工程结算产成品信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@DubboService
@Slf4j
public class ProductSettlementEndProductionServiceImpl extends JiHongServiceImpl<ProductSettlementEndProductionMapper, ProductSettlementEndProductionPO>
        implements IProductSettlementEndProductionService {


    @Resource
    private IProductLastPalletService iProductLastPalletService;

    @Resource
    private IProductSettlementEndProductionService iProductSettlementEndProductionService;

    @Resource
    private IProductTicketService iProductTicketService;

    @DubboReference
    private ISfcbTService iSfcbTService;

    @Resource
    private ProductDefectiveProductsMapper productDefectiveProductsMapper;

    @Override
    public void collect(String productTicketNo,Long productTicketId,Map<String,Integer> processSeqMap) {
        // 旧的结算记录
        List<GetSettlementEndProductionListOutVO> outVOList = iProductSettlementEndProductionService.getSettlementEndProductionList(productTicketNo);
        List<ProductSettlementEndProductionPO> settlementEndProductionPOList = BeanUtil.copyToList(outVOList, ProductSettlementEndProductionPO.class);
        Map<String, ProductSettlementEndProductionPO> settlementEndProductMap = settlementEndProductionPOList.stream().collect(Collectors.toMap(ProductSettlementEndProductionPO::getProcessName,t->t));

        ProductTicketPO productTicketPO = iProductTicketService.getById(productTicketId);
        GetListByPalletSourceInVO inVO = new GetListByPalletSourceInVO();
        inVO.setProductTicket(productTicketId);
        List<GetListByPalletSourceOutVO> palletSourceList = iProductLastPalletService.getListByPalletSource(inVO);
        if(CollectionUtil.isNotEmpty(palletSourceList)) {
            ProductSettlementEndProductionPO endProductionPO = settlementEndProductMap.get(productTicketPO.getProcess());
            if(Objects.isNull(endProductionPO)){
                endProductionPO = new ProductSettlementEndProductionPO();
                endProductionPO.setProductTicketNo(productTicketNo);
                endProductionPO.setProcessName(productTicketPO.getProcess());
                endProductionPO.setProcessSeq(processSeqMap.get(productTicketPO.getProcess()));
                endProductionPO.setProductQuantity(productTicketPO.getRealProduct());
                endProductionPO.setCreateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
            }else{
                endProductionPO.setUpdateBy(Objects.nonNull(SecurityUtil.getUserId())?SecurityUtil.getUserId():null);
                endProductionPO.setUpdateTime(new Date());
            }
            endProductionPO.setConsumptionQuantity(palletSourceList.stream().map(GetListByPalletSourceOutVO::getConsumptionQuantity).reduce(BigDecimal.ZERO,BigDecimal::add));
            endProductionPO.setRemainingQuantity(palletSourceList.stream().map(GetListByPalletSourceOutVO::getRemainingQuantity).reduce(BigDecimal.ZERO,BigDecimal::add));
            saveOrUpdate(endProductionPO);
        }
    }


    @Override
    public List<GetSettlementEndProductionListOutVO> getSettlementEndProductionList(String productTicketNo) {
        /*LambdaQueryWrapper<ProductSettlementEndProductionPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductSettlementEndProductionPO::getProductTicketNo,productTicketNo);
        List<ProductSettlementEndProductionPO> list = list(wrapper);*/

        List<GetProcessSeqByTickNoOutVO> processSeqList = iSfcbTService.getProcessSeqByTickNo(productTicketNo);
        Map<String, Integer> sfcbMap = processSeqList.stream().collect(Collectors.toMap(GetProcessSeqByTickNoOutVO::getProcessName,GetProcessSeqByTickNoOutVO::getProcessSeq));
        List<GetSettlementEndProductionListOutVO> list = baseMapper.getSettlementEndProductionList(productTicketNo);

        GetDefectListByTicketNoAndProcessInVO inVO = new GetDefectListByTicketNoAndProcessInVO();
        inVO.setProductTicketNo(productTicketNo);
        List<GetDefectListByTicketNoAndProcessOutVO> defectList = productDefectiveProductsMapper.getDefectListByTicketNoAndProcess(productTicketNo);

        // 各个工序的不良数量
        Map<String, GetDefectListByTicketNoAndProcessOutVO> defectMap = defectList.stream().collect(Collectors.toMap(GetDefectListByTicketNoAndProcessOutVO::getProcess, o -> o));

        list = list.stream().peek(x->x.setProcessSeq(sfcbMap.get(x.getProcessName()))).collect(Collectors.toList());
        // 各个工序的顺序
        Map<Integer, GetSettlementEndProductionListOutVO> processSeqMap = list.stream().collect(Collectors.toMap(GetSettlementEndProductionListOutVO::getProcessSeq, o -> o));
        return list.stream().peek(t->{
            GetSettlementEndProductionListOutVO next = null;
            if(Objects.nonNull(t.getProcessSeq())){
                next = processSeqMap.get(t.getProcessSeq() + 1);
            }
            if(Objects.nonNull(next)){
                t.setNextProductQuantity(next.getProductQuantity());
                t.setNextScrapQuantity(defectMap.get(t.getProcessName()).getDefectiveProductsQuantity());
                if(Objects.nonNull(t.getConsumptionQuantity()) && t.getConsumptionQuantity().compareTo(BigDecimal.ZERO)!=0) {
                    t.setDataVarianceDegree(next.getProductQuantity().add(t.getNextScrapQuantity()).divide(t.getConsumptionQuantity(),2, RoundingMode.HALF_UP));
                    t.setOutputRate(next.getProductQuantity().divide(t.getConsumptionQuantity(),2, RoundingMode.HALF_UP));
                }
            }
        }).sorted(Comparator.comparing(GetSettlementEndProductionListOutVO::getProcessSeq)).collect(Collectors.toList());
    }


    @Override
    public Pagination<GetSettlementEndProductionCollectDetailOutVO> getSettlementEndProductionCollectDetail(GetSettlementEndProductionCollectDetailInVO inVO) {
        Page<GetSettlementEndProductionCollectDetailOutVO> page = baseMapper.getSettlementEndProductionCollectDetail(inVO.getPage(), inVO);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }

    @Override
    public Pagination<ProductPalletPO> getSettlementEndProductionDetail(GetSettlementEndProductionDetailInVO inVO) {
        List<Long> productTickIdList = iProductTicketService.getListByPlanTicketNo(inVO.getProductTicketNo(), TicketStatusEnum.COMPLETED.getCode()).stream().map(GetListByPlanTicketNoOutVO::getId).collect(Collectors.toList());
        return iProductLastPalletService.getListByProductTickIds(inVO.getPageNum().longValue(),inVO.getPageSize().longValue(),productTickIdList,inVO.getProcessName());
    }


}
