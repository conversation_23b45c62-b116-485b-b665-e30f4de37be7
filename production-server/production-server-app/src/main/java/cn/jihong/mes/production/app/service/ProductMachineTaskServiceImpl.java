package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.logistics.api.model.response.ApiOrderCarryInstructionResponse;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.model.vo.MachineConfigInVO;
import cn.jihong.mes.api.service.IProductionMachineConfigService;
import cn.jihong.mes.api.service.IProductionMachineLogisticsConfigService;
import cn.jihong.mes.production.api.model.constant.LogisticsConst;
import cn.jihong.mes.production.api.model.enums.MachineTaskTypeEnum;
import cn.jihong.mes.production.api.model.enums.ProductConfigBillingTypeEnum;
import cn.jihong.mes.production.api.model.enums.SiteEnum;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskHistoryPO;
import cn.jihong.mes.production.api.model.po.ProductMachineTaskPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.in.logistics.CancelOrderCarryInVO;
import cn.jihong.mes.production.api.model.vo.in.logistics.GetInstructionByTargetDeviceIdInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.config.JinshanLogisticsConfig;
import cn.jihong.mes.production.app.factory.MachineTaskActionFactory;
import cn.jihong.mes.production.app.mapper.ProductMachineTaskMapper;
import cn.jihong.mes.production.app.util.ThreadPoolUtil;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.model.vo.SfcbTVO;
import cn.jihong.oa.erp.api.service.IEcaaucTService;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import cn.jihong.tms.api.model.enums.TicketStatusEnum;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import cn.jihong.mes.production.api.model.constant.CompanyProcessConst;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * <p>
 * 生产机台任务 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-19
 */
@Slf4j
@DubboService
public class ProductMachineTaskServiceImpl extends JiHongServiceImpl<ProductMachineTaskMapper, ProductMachineTaskPO> implements IProductMachineTaskService {

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");

    @Resource
    private IProductFinishOrderService iProductFinishOrderService;

    @Resource
    private IProductTicketService iProductTicketService;

    @Resource
    private IProductMachineTaskHistoryService iProductMachineTaskHistoryService;
    @DubboReference
    private IProductionMachineConfigService productionMachineConfigService;
    @Resource
    private IProductMachineTicketService productMachineTicketService;
    @Resource
    private IProductTicketService productTicketService;

    @DubboReference
    private IEcaaucTService iEcaaucTService;

    @DubboReference
    private IA01Service ia01Service;

    @Resource
    private JinshanLogisticsConfig jinshanLogisticsConfig;

    @Resource
    private ILogisticsService iLogisticsService;

    @Resource
    private IProductionMachineLogisticsConfigService iProductionMachineLogisticsConfigService;

    @DubboReference
    private ISfcbTService iSfcbTService;

    @Override
    public VerifyReportRequestOutVO verifyReportRequest(VerifyReportRequestInVO inVO) {
        VerifyReportRequestOutVO outVO = new VerifyReportRequestOutVO();
        outVO.setResult(true);
        // 处理报工数量为0情况
        if(inVO.getReportedQuantity().compareTo(BigDecimal.ZERO) == 0){
            return outVO;
        }

        ProductTicketPO productTicketPO = productTicketService.getById(inVO.getReportedProductId());
        SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        // 首道工序报工 需校验报工数量
        if("INIT".equals(sfcbTVO.getSfcb007())) {
            BigDecimal totalReportQuantity = sfcbTVO.getSfcb033()!=null?sfcbTVO.getSfcb033():BigDecimal.ZERO;
            BigDecimal totalScrapQuantity = sfcbTVO.getSfcb036()!=null?sfcbTVO.getSfcb036():BigDecimal.ZERO;
            BigDecimal canReportQuantity = sfcbTVO.getSfcb028().subtract(totalReportQuantity).subtract(totalScrapQuantity);
            if(inVO.getReportedQuantity().compareTo(canReportQuantity) > 0) {
                // 总报工数量 大于 开单数量
                outVO.setResult(false);
            }
            outVO.setBillingQuantity(sfcbTVO.getSfcb028());
            outVO.setTotalReportQuantity(totalReportQuantity);
            outVO.setCanReportQuantity(canReportQuantity);
        }
        log.info("verifyInboundRequest:{}", JSON.toJSONString(outVO) );
        return outVO;
    }

    @RedisLock
    @Override
    public Boolean saveMachineTask(String key,SaveMachineTaskInVO inVO){
        // 校验 机台和任务id是否相同
        Long productionTicketNo = productTicketService.getProductionTicketNo(inVO.getMachineName());
        if (inVO.getReportedProductId() != null  && !inVO.getReportedProductId().equals(productionTicketNo)) {
            throw new CommonException("机台和任务id不匹配，请刷新页面重试");
        }


        String companyCode = SecurityUtil.getCompanySite();
        if("SITE-22".equals(companyCode) || "SITE-01".equals(companyCode)) {
            IMachineTaskAction machineTaskActionImpl = MachineTaskActionFactory.getImpl(inVO.getType());
            if (machineTaskActionImpl != null) {
                // 个性化 机台状态处理
                return machineTaskActionImpl.execute(inVO);
            }
        }
        return switchMachineTask(inVO);
    }

    public Boolean switchMachineTask(SaveMachineTaskInVO inVO) {

        MachineTaskTypeEnum newMachineTaskTypeEnum = MachineTaskTypeEnum.getMachineTaskTypeEnum(inVO.getType());
        ProductTicketPO productTicketPO = null;
        if(Objects.equals(newMachineTaskTypeEnum.getParentCode(),MachineTaskTypeEnum.IN_PRODUCTION.getCode())){
            productTicketPO = iProductTicketService.getById(inVO.getReportedProductId());
            if(Objects.isNull(productTicketPO)){
                throw new CommonException("请先在生产任务设置中创建生产工单");
            }
            if(!Objects.equals(productTicketPO.getStatus(), TicketStatusEnum.IN_PROGRESS.getCode())){
                throw new CommonException("工单不是处理中状态，工单" + productTicketPO.getTicketRequestId());
            }
        }

        GetMachineTaskByNameInVO getMachineTaskByNameInVO = new GetMachineTaskByNameInVO();
        getMachineTaskByNameInVO.setMachineName(inVO.getMachineName());
        GetMachineTaskByNameOutVO machineTaskByName = getMachineTaskByName(getMachineTaskByNameInVO);
        if(Objects.nonNull(machineTaskByName) && Objects.equals(machineTaskByName.getFinished(),BooleanEnum.FALSE.getCode())){

            if(Objects.nonNull(inVO.getOriginalType()) && !Objects.equals(machineTaskByName.getType(),inVO.getOriginalType())){
                throw new CommonException("机台状态已改变，请刷新页面，重试");
            }

             // 当前机台状态存在尚未结束
            MachineTaskTypeEnum currentMachineTaskTypeEnum = MachineTaskTypeEnum.getMachineTaskTypeEnum(machineTaskByName.getType());
            if(Objects.equals(currentMachineTaskTypeEnum.getParentCode(),MachineTaskTypeEnum.IN_PRODUCTION.getCode())) {
                // 都是处于父级生产状态 目前包含生产和调机
                if(Objects.equals(newMachineTaskTypeEnum.getCode(),currentMachineTaskTypeEnum.getCode())) {
                    // 相同机台状态下报工
                    ProductMachineTaskPO machineTaskPO = getById(machineTaskByName.getId());
                    if(Objects.nonNull(inVO.getReportedQuantity())) {
                        machineTaskPO.setReportedQuantity(Objects.isNull(machineTaskByName.getReportedQuantity()) ? inVO.getReportedQuantity()
                                : machineTaskByName.getReportedQuantity().add(inVO.getReportedQuantity()));
                    }

                    if(productTicketPO == null){
                        productTicketPO = iProductTicketService.getById(inVO.getReportedProductId());
                    }

                    if (inVO.getReportShift() != null && productTicketPO.getReportShift() == null) {
                        // 设置报工班次
                        productTicketPO.setReportShift(inVO.getReportShift());
                    } else if (inVO.getReportShift() != null && !productTicketPO.getReportShift().equals(inVO.getReportShift())) {
                        throw new CommonException("报工班次已选定,请刷新页面");
                    }

                    machineTaskPO.setReportedProductId(inVO.getReportedProductId());
                    EcaaucTPO ecaaucTPO = iEcaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
                    if (ecaaucTPO != null) {
                        machineTaskPO.setReportedUnit(ecaaucTPO.getEcaauc009());
                    }
                    // 生产中 继续报工
                    iProductMachineTaskHistoryService.saveMachineTaskHistory(machineTaskPO,
                            inVO.getReportedQuantity(),inVO.getReason(),inVO.getProductOutboundId());
                    // 修改 计件类型
                    if (StringUtils.isNotBlank(inVO.getPieceType())) {
                        productTicketPO.setPieceType(inVO.getPieceType());
                    }
                    // 第一次报工 更新报工班次
                    if(productTicketPO.getReportShift() == null && inVO.getReportShift()!=null){
                        productTicketPO.setReportShift(inVO.getReportShift());
                    }
                    iProductTicketService.updateById(productTicketPO);
                    return updateById(machineTaskPO);
                }
            }

            if(Objects.equals(inVO.getType(),machineTaskByName.getType())){
                throw new CommonException("当前机台已经处于该任务状态");
            }
        }


        if (productTicketPO == null) {
            productTicketPO = iProductTicketService.getById(inVO.getReportedProductId());
        }
        // 修改 计件类型
        if (StringUtils.isNotBlank(inVO.getPieceType())) {
            productTicketPO.setPieceType(inVO.getPieceType());
        }
        // 第一次报工 更新报工班次
        if(productTicketPO.getReportShift() == null && inVO.getReportShift()!=null){
            productTicketPO.setReportShift(inVO.getReportShift());
        }
        iProductTicketService.updateById(productTicketPO);

        return updateMachineTask(inVO.getMachineName(),inVO.getType(),inVO.getReportedProductId(),inVO.getReportedQuantity(),inVO.getReason());
    }

    /**
     * 更新机台任务状态
     * @param machineName
     * @param type
     * @param reportedProductId
     * @param reportedQuantity
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024/2/23 15:12
     */
    @Override
    public Boolean updateMachineTask(String machineName,Integer type,Long reportedProductId, BigDecimal reportedQuantity,String reason) {
        stopMachineTask(machineName,reportedQuantity,reason);
        if(Objects.nonNull(type)){
            startMachineTask(machineName,type,reportedProductId);
        }
        return true;
    }


    @Override
    public Boolean stopMachineTask(String machineName,BigDecimal reportedQuantity,String reason) {
        GetMachineTaskByNameInVO getMachineTaskByNameInVO = new GetMachineTaskByNameInVO();
        getMachineTaskByNameInVO.setMachineName(machineName);
        GetMachineTaskByNameOutVO machineTaskByName = getMachineTaskByName(getMachineTaskByNameInVO);

        if(Objects.nonNull(machineTaskByName)){
            // 结束当前机台任务状态
            ProductMachineTaskPO productMachineTaskPO = getById(machineTaskByName.getId());
            productMachineTaskPO.setFinished(BooleanEnum.TRUE.getCode());
            productMachineTaskPO.setUpdateBy(SecurityUtil.getUserId());
            productMachineTaskPO.setUpdateTime(new Date());
            productMachineTaskPO.setEndTime(new Date());

            if(Objects.nonNull(reportedQuantity)) {
                productMachineTaskPO.setReportedQuantity(Objects.isNull(productMachineTaskPO.getReportedQuantity()) ? reportedQuantity
                        : productMachineTaskPO.getReportedQuantity().add(reportedQuantity));

                if(productMachineTaskPO.getReportedProductId() != null) {
                    ProductTicketPO productTicketPO = iProductTicketService.getById(productMachineTaskPO.getReportedProductId());
                    EcaaucTPO ecaaucTPO = iEcaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
                    if (ecaaucTPO != null) {
                        productMachineTaskPO.setReportedUnit(ecaaucTPO.getEcaauc009());
                    }
                }
            }
            updateById(productMachineTaskPO);
            iProductMachineTaskHistoryService.saveMachineTaskHistory(productMachineTaskPO,reportedQuantity,reason,null);
        }
        return true;
    }

    @Override
    public Boolean stopMachineTaskById(Long machineTaskId){
        // 结束当前机台任务状态
        ProductMachineTaskPO productMachineTaskPO = getById(machineTaskId);
        productMachineTaskPO.setFinished(BooleanEnum.TRUE.getCode());
        productMachineTaskPO.setUpdateBy(SecurityUtil.getUserId());
        productMachineTaskPO.setUpdateTime(new Date());
        productMachineTaskPO.setEndTime(new Date());

        updateById(productMachineTaskPO);
        return iProductMachineTaskHistoryService.saveMachineTaskHistory(productMachineTaskPO,null,null,null);
    }

    public void startMachineTask(String machineName, Integer type,Long reportedProductId) {
        ProductMachineTaskPO productMachineTaskPO = new ProductMachineTaskPO();
        productMachineTaskPO.setMachineName(machineName);
        productMachineTaskPO.setType(type);
        productMachineTaskPO.setReportedProductId(reportedProductId);
        productMachineTaskPO.setFinished(BooleanEnum.FALSE.getCode());
        productMachineTaskPO.setStartTime(new Date());
        productMachineTaskPO.setCreateBy(SecurityUtil.getUserId());

        save(productMachineTaskPO);
        iProductMachineTaskHistoryService.saveMachineTaskHistory(productMachineTaskPO, null,null,null);
    }


    @Override
    public GetMachineTaskByNameOutVO getMachineTaskByName(GetMachineTaskByNameInVO inVO) {
        LambdaQueryWrapper<ProductMachineTaskPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskPO::getMachineName,inVO.getMachineName())
      //          .eq(Objects.nonNull(inVO.getReportedProductId()),ProductMachineTaskPO::getReportedProductId,inVO.getReportedProductId())
                .eq(ProductMachineTaskPO::getFinished,BooleanEnum.FALSE.getCode())
                .orderByDesc(ProductMachineTaskPO::getCreateTime).last("limit 1");
        ProductMachineTaskPO productMachineTaskPO = getOne(wrapper);
        GetMachineTaskByNameOutVO outVO = new GetMachineTaskByNameOutVO();
        if(Objects.nonNull(productMachineTaskPO)){
            BeanUtil.copyProperties(productMachineTaskPO,outVO);
            outVO.setTypeName(MachineTaskTypeEnum.getMachineTaskTypeEnum(outVO.getType()).getName());
            return outVO;
        }else {
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @RedisLock
    @Override
    public Boolean  finishMachineTask(String key,FinishMachineTaskInVO inVO) {
        ProductMachineTaskPO productMachineTaskPO = getById(inVO.getId());

        GetMachineTaskByNameInVO getMachineTaskByNameInVO = new GetMachineTaskByNameInVO();
        getMachineTaskByNameInVO.setMachineName(productMachineTaskPO.getMachineName());
        GetMachineTaskByNameOutVO machineTaskByName = getMachineTaskByName(getMachineTaskByNameInVO);
        if(Objects.nonNull(inVO.getOriginalType()) && Objects.nonNull(machineTaskByName) && !Objects.equals(machineTaskByName.getType(),inVO.getOriginalType())){
            throw new CommonException("机台状态已改变，请刷新页面，重试");
        }

        // 结束该机台未完成的生产工单
        ProductTicketPO productionTicket = iProductTicketService.getProductionTicket(productMachineTaskPO.getMachineName());
        if(Objects.nonNull(productionTicket)){
            // 下机不去报工  直接修改数量为0
            inVO.setReportedQuantity(BigDecimal.ZERO);
            if(Objects.nonNull(inVO.getReportedQuantity())) {
                productMachineTaskPO.setReportedQuantity(Objects.isNull(productMachineTaskPO.getReportedQuantity()) ? inVO.getReportedQuantity()
                        : productMachineTaskPO.getReportedQuantity().add(inVO.getReportedQuantity()));
            }
            if(Objects.equals(inVO.getFinished(),BooleanEnum.TRUE.getCode())) {
                SaveFinishOrderInfoInVO saveFinishOrderInfoInVO = new SaveFinishOrderInfoInVO();
                saveFinishOrderInfoInVO.setMachineName(productMachineTaskPO.getMachineName());
                saveFinishOrderInfoInVO.setProductTicketId(inVO.getReportedProductId());
                iProductFinishOrderService.saveFinishOrderInfo(saveFinishOrderInfoInVO);

                // 获得结单类型
                MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                        .machineName(productMachineTaskPO.getMachineName())
//                        .processCode(productionTicket.getProcessCode())
                        .build();
                Integer billingType = productionMachineConfigService.getBillingType(machineConfigInVO);
                if (ProductConfigBillingTypeEnum.ONE_DAY.getIntCode().equals(billingType)) {
                    // 如果是一日一结，需要把机台和工程单的状态解绑
                    productMachineTicketService.updateByMachineName(productionTicket.getMachineName(),productionTicket.getPlanTicketNo());
                }
            }
        }

        // 长期待机的状态下机，不操作机台的任务状态
        MachineTaskTypeEnum currentMachineTaskTypeEnum = MachineTaskTypeEnum.getMachineTaskTypeEnum(productMachineTaskPO.getType());
        if(!Objects.equals(MachineTaskTypeEnum.LONG_STANDBY.getCode(),currentMachineTaskTypeEnum.getParentCode())) {
            updateMachineTask(productMachineTaskPO.getMachineName(),null,productMachineTaskPO.getReportedProductId(),inVO.getReportedQuantity(),inVO.getReason());
        }
        if(jinshanLogisticsConfig.getCompanyCodes().contains(SecurityUtil.getCompanySite()) && productionTicket!=null && !LogisticsConst.TILE_WIRE_PROCESS.contains(productionTicket.getProcessCode()) && !LogisticsConst.SLITTER_PROCESS.contains(productionTicket.getProcessCode())) {
             // 关闭供料计划
            ThreadPoolUtil.getInstance().execute(()-> closeFeedPlan(productionTicket));
        }
        return true;
    }

    /**
     * 关闭供料计划
     * @param productionTicket
     * @return: void
     * <AUTHOR>
     * @date: 2025-03-14 10:40
     */
    private void closeFeedPlan(ProductTicketPO productionTicket) {
        List<String> previoutErpOrderIdList = iLogisticsService.getPrevioutErpOrderIdList(productionTicket.getPlanTicketNo(), productionTicket.getProcessCode());
        ProductionMachineLogisticsConfigPO machineConfig = iProductionMachineLogisticsConfigService.getMachineConfig(productionTicket.getMachineName(), productionTicket.getProcessCode());
        String[] split = machineConfig.getSupplyMeshCode().split(",");
        for (String supplyMeshCode : split) {
            GetInstructionByTargetDeviceIdInVO instruction = new GetInstructionByTargetDeviceIdInVO();
            instruction.setProductTicketId(productionTicket.getId());
            instruction.setTargetMeshId(supplyMeshCode);
            List<ApiOrderCarryInstructionResponse> list = iLogisticsService.getInstructionByTargetDeviceId(instruction);
            list.forEach(instructionResponse -> {
                String erpOrderId = instructionResponse.getOrderItem().getERPOrderId();
                // 关闭对应当前工序的供料计划
                if (previoutErpOrderIdList.contains(erpOrderId)) {
                    CancelOrderCarryInVO cancelOrderCarryInVO = new CancelOrderCarryInVO();
                    cancelOrderCarryInVO.setProductTicketId(productionTicket.getId());
                    cancelOrderCarryInVO.setOrderCarryInstructionId(instructionResponse.getOrderCarryInstructionId());
                    iLogisticsService.cancelOrderCarry(cancelOrderCarryInVO);
                }
            });
        }
    }


    @Override
    public List<ProductMachineTaskPO> getListByProductId(Long productId) {
        LambdaQueryWrapper<ProductMachineTaskPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskPO::getReportedProductId,productId);
        return list(wrapper);
    }

    @Override
    public List<ProductMachineTaskPO> getListByProductId(Long productId, Integer type) {
        LambdaQueryWrapper<ProductMachineTaskPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskPO::getReportedProductId,productId)
                .eq(ProductMachineTaskPO::getType,type);
        return list(wrapper);
    }

    @Override
    public List<ProductMachineTaskPO> getByProductTicketIds(List<Long> productTicketIds) {
        if (CollectionUtil.isEmpty(productTicketIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ProductMachineTaskPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(ProductMachineTaskPO::getReportedProductId,productTicketIds);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<GetMachineTaskTypeEnumOutVO> getMachineTaskTypeEnum() {
        List<MachineTaskTypeEnum> list = Arrays.stream(MachineTaskTypeEnum.values()).filter(MachineTaskTypeEnum::getShow).collect(Collectors.toList());
        return BeanUtil.copyToList(list,GetMachineTaskTypeEnumOutVO.class);
    }

    @Override
    public List<GetLongStandbyMachineTaskTypeEnumOutVO> getLongStandbyMachineTaskTypeEnum() {
        List<MachineTaskTypeEnum> list = Arrays.stream(MachineTaskTypeEnum.values()).filter(t->Objects.equals(t.getParentCode(),MachineTaskTypeEnum.LONG_STANDBY.getCode())).collect(Collectors.toList());
        return BeanUtil.copyToList(list,GetLongStandbyMachineTaskTypeEnumOutVO.class);
    }

    @Override
    public Pagination<GetProductMachineTaskPageOutVO> getProductMachineTaskPage(GetProductMachineTaskPageInVO inVO) {
        Page<GetProductMachineTaskPageOutVO> taskPage = baseMapper.getProductMachineTaskPage(inVO.getPage(),inVO,SecurityUtil.getCompanySite());
        if(CollectionUtil.isNotEmpty(taskPage.getRecords())) {
            String reportedUserIds = taskPage.getRecords().stream().map(t -> String.valueOf(t.getReportPersonId())).collect(joining(","));
            String teamUserIds = taskPage.getRecords().stream().map(GetProductMachineTaskPageOutVO::getTeamUsers).collect(joining(","));
            Map<Long, UserDTO> userNameMap = getUserNameMap(reportedUserIds + "," + teamUserIds);
            taskPage.getRecords().stream().peek(t -> {
                t.setReportPersonName(userNameMap.get(t.getReportPersonId()) != null?userNameMap.get(t.getReportPersonId()).getName():"");

                if (chinesePattern.matcher(t.getTeamUsers()).find()) {
                    t.setTeamUsersName(t.getTeamUsers());
                } else {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    for (String teamUserId : t.getTeamUsers().split(",")) {
                        if(StringUtils.isNotBlank(teamUserId)) {
                            UserDTO userDTO = userNameMap.get(Long.parseLong(teamUserId));
                            stringJoiner.add(userDTO != null? userDTO.getName():"");
                        }
                    }
                    t.setTeamUsersName(stringJoiner.toString());
                }
                t.setTypeName(MachineTaskTypeEnum.getMachineTaskTypeEnum(t.getType()).getName());
                if(Objects.nonNull(t.getStartTime()) && Objects.nonNull(t.getEndTime())) {
                    BigDecimal consumingHours = new BigDecimal(t.getEndTime().getTime() - t.getStartTime().getTime())
                            .divide(new BigDecimal(1000 * 60 * 60), 2, RoundingMode.HALF_UP);
                    t.setConsumingHours(consumingHours);
                }
            }).collect(toList());
            return Pagination.newInstance(taskPage);
        }else {
            return Pagination.newInstance(null, 0, 0);
        }
    }

    private Map<Long, UserDTO> getUserNameMap(String userIds) {
        String[] userIdAry = userIds.split(",");
        List<Long> userLong = Arrays.stream(userIdAry).filter(userId->{
            return !chinesePattern.matcher(userId).find();
        }).map(Long::valueOf).collect(toList());
        List<UserDTO> userInfoByIds = ia01Service.getUserInfoByIds(userLong);
        return userInfoByIds.stream().collect(toMap(UserDTO::getId, Function.identity()));
    }

    @Override
    public List<ProductMachineTaskPO> getByProductTicketId(Long productTicketId, Integer type) {
        LambdaQueryWrapper<ProductMachineTaskPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductMachineTaskPO::getReportedProductId,productTicketId)
                .eq(ProductMachineTaskPO::getType,type);
        return list(wrapper);
    }

    @Override
    public void updateMachineTaskHistory(BigDecimal orgProducedQuantity,
                                         UpdateOutboundInVO updateOutboundInVO, ProductTicketPO productTicketPO) {
//        updateOutboundInVO.getOutboundId()  去更新报工信息 及 更新汇总信息
        if (
                (org.apache.commons.lang3.StringUtils.equals(SecurityUtil.getCompanySite(), SiteEnum.AHGC.getCode())
                        && CompanyProcessConst.moqieList_anhui.contains(productTicketPO.getProcessCode()))
                ||
                (org.apache.commons.lang3.StringUtils.equals(SecurityUtil.getCompanySite(), SiteEnum.LFEQ.getCode())
                        && CompanyProcessConst.moqieList_langfang.contains(productTicketPO.getProcessCode())
                )
        ) {
            log.info("当前是模切工序，需要将小片转成大张");
            // 个转张 ： 个数 / 模数
            SfcbTVO sfcbTVO = iSfcbTService.getByTickNoAndProcess(
                    productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
            BigDecimal sfcbud016 = sfcbTVO.getSfcbud016();
            log.info("个转米  ：  个数 / 模数   : {}/{}", updateOutboundInVO.getProducedQuantity(), sfcbud016);
            BigDecimal newQuantity = updateOutboundInVO.getProducedQuantity().divide(sfcbud016, 0, RoundingMode.HALF_UP);
            updateOutboundInVO.setProducedQuantity(newQuantity);
        }

        ProductMachineTaskHistoryPO productMachineTaskHistoryPO = iProductMachineTaskHistoryService.updateByOutboundId(updateOutboundInVO);
        if (productMachineTaskHistoryPO == null) {
            return;
        }

        List<ProductMachineTaskHistoryPO> productMachineTaskHistoryPOS = iProductMachineTaskHistoryService.getHistoryListByProductMachineTaskId(productMachineTaskHistoryPO.getProductMachineTaskId(), 1);
        BigDecimal reduce = productMachineTaskHistoryPOS.stream().map(ProductMachineTaskHistoryPO::getReportedQuantity)
                .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 修改报工信息
        LambdaQueryWrapper<ProductMachineTaskPO> lambdaQueryWrapper = Wrappers.lambdaQuery(ProductMachineTaskPO.class)
                .eq(ProductMachineTaskPO::getId, productMachineTaskHistoryPO.getProductMachineTaskId());
        ProductMachineTaskPO one = getOne(lambdaQueryWrapper);
        one.setReportedQuantity(reduce);
        one.setUpdateBy(SecurityUtil.getUserId());
        one.setUpdateTime(new Date());
        updateById(one);
    }
}
