package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.api.model.po.ProductionPlanPO;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.ProductionPlanDTO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.in.logistics.CallMaterialInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.IProductTicketService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 生产工单信息表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@RestController
@RequestMapping("/productTicket")
@ShenyuSpringMvcClient(path = "/productTicket/**")
public class ProductTicketController {

    @Resource
    private IProductTicketService productTicketService;

    /**
     * 获取生产计划工程单列表
     */
    @PostMapping("/getProductPlanList")
    public StandardResult<List<ProductionPlanDTO>> getProductPlanList(@RequestBody @Valid GetProductPlanListVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productTicketService.getProductPlanList(vo));
    }

    /**
     * 创建生产工单并分配给自己
     * 
     * @param vo
     * @return {@link StandardResult}
     */
    @PostMapping("/createAndProcessor")
    public StandardResult createProductTicket(@RequestBody @Valid CreateProductTicketInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.createAndProcessor(vo.getMachineName(),vo));
    }

    /**
     * 叫料
     * @param vo
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2025-03-26 18:47
     */
    @PostMapping("/callMaterial")
    public StandardResult<String> callMaterial(@RequestBody @Valid CallMaterialInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.callMaterial(vo));
    }



    /**
     * 获得当前机台的工单
     * 
     * @param getProductionTicketNo
     * @return {@link StandardResult}
     */
    @PostMapping("/getProductionTicketNo")
    public StandardResult getProductionTicketNo(@RequestBody @Valid GetProductionTicketNoInVO getProductionTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productTicketService.getProductionTicketNo(getProductionTicketNo.getMachineName()));
    }

    /**
     * 获得当前机台的详情
     *
     * @param id
     * @return {@link StandardResult}
     */
    @GetMapping("/getProductionTicketInfo/{id}")
    public StandardResult<ProductionTicketInfoOutVO> getProductionTicketInfo(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productTicketService.getProductionTicketInfo(id));
    }

    /**
     * 获得生产中的详情
     *
     * @param id
     * @return {@link StandardResult}
     */
    @GetMapping("/getProductingTicketInfo/{id}")
    public StandardResult<ProductionTicketInfoOutVO> getProductingTicketInfo(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productTicketService.getProductingTicketInfo(id));
    }

    /**
     * 获得当前机台的设备止码
     *
     * @param id
     * @return {@link StandardResult}
     */
    @GetMapping("/getMachineStopNo/{id}")
    public StandardResult<Integer> getMachineStopNo(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productTicketService.getMachineStopNo(id));
    }

    /**
     * 获得生产工单信息
     */
    @PostMapping("/getProductTicketList")
    public StandardResult<Pagination<ProductionTicketInfoOutVO>>
    getProductTicketList(@RequestBody @Valid GetProductTicketListInVO getProductTicketListInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productTicketService.getProductTicketList(getProductTicketListInVO));
    }

    /**
     * 报工
     */
    @GetMapping("/signingUpForWork/{id}")
    public StandardResult<String> signingUpForWork(@PathVariable Long id) {
        String LOCK_KEY = RedisCacheConstant.REPORT  + id;
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.signingUpForWork(LOCK_KEY,id));
    }

    /**
     * 获得机台当前绑定的工程单
     * machineName 机台名称
     */
    @PostMapping("/getPalnTicketNo")
    public StandardResult<String> getPalnTicketNo(@RequestBody @Valid GetByMachineNameInVO getByMachineNameInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productTicketService.getPalnTicketNo(getByMachineNameInVO.getMachineName()));
    }

    /**
     * 获得工程单列表
     * palnTicketNo  工单号模糊字段
     */
    @GetMapping("/getPalnTicketNoList/{palnTicketNo}")
    public StandardResult<List<String>> getPalnTicketNoList(@PathVariable String palnTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.getPalnTicketNoList(palnTicketNo));
    }

    /**
     * 获得工单信息
     * @param planTicketNo
     * @return
     */
    @GetMapping("/getErpTicketInfo/{planTicketNo}")
    public StandardResult<GetErpTicketInfoOutVO> getTicketInfo(@PathVariable String planTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getTicketInfo(planTicketNo));
    }


    /**
     * 获得工单信息
     * @param planTicketNo
     * @return
     */
    @GetMapping("/getErpTicketDetailInfo/{planTicketNo}")
    public StandardResult<GetErpTicketInfoOutVO> getTicketDetailInfo(@PathVariable String planTicketNo) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getTicketDetailInfo(planTicketNo));
    }

    /**
     * 获得计件类型
     * @param getValuationTypeInVO
     * @return
     */
    @PostMapping("/getValuationType")
    public StandardResult<List<GetValuationTypeOutVO>> getValuationType(@RequestBody @Valid GetValuationTypeInVO getValuationTypeInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getValuationType(getValuationTypeInVO));
    }


    /**
     * 修改计件类型
     * @param inVO
     * @return
     */
    @PostMapping("/updatePieceType")
    public StandardResult<Boolean> updatePieceType(@RequestBody @Valid UpdatePieceTypeInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.updatePieceType(inVO.getProductTicketId(),inVO.getPieceType()));
    }

    /**
     * 获得上一个任务的任务信息
     */
    @PostMapping("/getLastTaskInfo")
    public StandardResult<GetLastTaskInfoOutVO> getLastTaskInfo(@RequestBody @Valid GetLastTaskInfoInVO vo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productTicketService.getLastTaskInfo(vo));
    }


    /**
     * 获得报工班次
     * @param productTicketId
     * @return
     */
    @GetMapping("/getReportShit/{productTicketId}")
    public StandardResult<List<GetReportShiftNoOutVO>> getReportShit(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getReportShit(productTicketId));
    }


    /**
     * 获得报工班次
     * @param shit
     * @return
     */
    @GetMapping("/getReportShitByShit/{shit}")
    public StandardResult<List<GetReportShiftNoOutVO>> getReportShitByShit(@PathVariable Integer shit) {
        return StandardResult.resultCode(OperateCode.SUCCESS, productTicketService.getReportShitByShit(shit));
    }

    /**
     * 获取瓦线生产计划工程单列表
     */
    @PostMapping("/getTileWireProductPlanList")
    public StandardResult<List<ProductionPlanPO>> getTileWireProductPlanList(@RequestBody @Valid GetTileWireProductPlanListInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.getTileWireProductPlanList(inVO));
    }

    /**
     * 同步选择的计划给瓦线
     */
    @PostMapping("/tileWirePostOrder")
    public StandardResult<Boolean> tileWirePostOrder(@RequestBody @Valid List<Long> productionPlanIdList) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.tileWirePostOrder(productionPlanIdList));
    }

    /**
     * 撤销良品报工
     * @param id
     * @return
     */
    @GetMapping("/cancelGoodsReport/{id}")
    public StandardResult<Boolean> cancelGoodsReport(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.cancelGoodsReport(id));
    }

    /**
     * 撤销不良品报工
     * @param id
     * @return
     */
    @GetMapping("/cancelBadGoodsReport/{id}")
    public StandardResult<Boolean> cancelBadGoodsReport(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.cancelBadGoodsReport(id));
    }

    /**
     * 撤销倒扣料件
     * @param id
     * @return
     */
    @GetMapping("/cancelMaterialReport/{id}")
    public StandardResult<Boolean> cancelMaterialReport(@PathVariable Long id) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.cancelMaterialReport(id));
    }


    /**
     * 校验工序的数量
     * @param checkProcessNumInVO
     * @return
     */
    @PostMapping("/checkProcessNum")
    public StandardResult<String> checkProcessNum(@RequestBody @Valid CheckProcessNumInVO checkProcessNumInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.checkProcessNum(checkProcessNumInVO));
    }


    /**
     * 校验工单的入库数量
     * @param checkTicketInNumInVO
     * @return
     */
    @PostMapping("/checkTicketInNum")
    public StandardResult<String> checkTicketInNum(@RequestBody @Valid CheckTicketInNumInVO checkTicketInNumInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,productTicketService.checkTicketInNum(checkTicketInNumInVO));
    }

}
