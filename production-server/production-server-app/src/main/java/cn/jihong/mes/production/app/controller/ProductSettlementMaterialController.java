package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.po.ProductMaterialPO;
import cn.jihong.mes.production.api.model.vo.in.GetSettlementMaterialDetailInVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetSettlementMaterialListOutVO;
import cn.jihong.mes.production.api.service.IProductSettlementMaterialService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工程结算物料信息
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@RestController
@RequestMapping("/productSettlementMaterial")
@ShenyuSpringMvcClient(path = "/productSettlementMaterial/**")
public class ProductSettlementMaterialController {


    @Autowired
    private IProductSettlementMaterialService iProductSettlementMaterialService;

    /**
     * 查询物料信息
     * @param productTicketNo
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/8 16:01
     */
    @GetMapping("/getSettlementMaterialList")
    public StandardResult<List<GetSettlementMaterialListOutVO>> getSettlementMaterialList(@RequestParam String productTicketNo){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementMaterialService.getSettlementMaterialList(productTicketNo));
    }

    /**
     * 查询物料详情汇总信息列表
     * @param inVO
     * @return: cn.jihong.common.model.StandardResult
     * <AUTHOR>
     * @date: 2023/11/14 17:52
     */
    @PostMapping("/getSettlementMaterialDetail")
    public StandardResult<Pagination<GetSettlementMaterialDetailOutVO>> getSettlementMaterialDetail(@RequestBody @Valid GetSettlementMaterialDetailInVO inVO){
        return StandardResult.resultCode(OperateCode.SUCCESS,iProductSettlementMaterialService.getSettlementMaterialDetail(inVO));
    }
}

