package cn.jihong.mes.production.app.service;

import cn.jihong.common.util.CollectionUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.po.ProductUserShiftPO;
import cn.jihong.mes.production.api.model.vo.in.ProductShiftInVO;
import cn.jihong.mes.production.api.service.IProductUserShiftService;
import cn.jihong.mes.production.app.mapper.ProductUserShiftMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工班组信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@DubboService
public class ProductUserShiftServiceImpl extends JiHongServiceImpl<ProductUserShiftMapper, ProductUserShiftPO> implements IProductUserShiftService {

    @Override
    public Long saveProductShift(ProductShiftInVO productShiftInVO) {
        List<ProductUserShiftPO> productShiftPOS = productShiftInVO.getTeamUsers().stream().distinct().map(
                productShift -> {
            ProductUserShiftPO productShiftPO = new ProductUserShiftPO();
            productShiftPO.setCompanyCode(SecurityUtil.getCompanySite());
            productShiftPO.setUserId(Long.valueOf(productShift.getUserId()));
            productShiftPO.setRoleCode(productShift.getRoleCode());
            productShiftPO.setRoleName(productShift.getRoleName());
            productShiftPO.setTeamUsers(productShift.getUserId());
            productShiftPO.setCreateBy(SecurityUtil.getUserId());
            return productShiftPO;
        }).collect(Collectors.toList());
        saveBatch(productShiftPOS);
        return 1L;
    }

    @Override
    public ProductShiftInVO getProductShift() {
        ProductShiftInVO productShiftInVO = new ProductShiftInVO();
        LambdaQueryWrapper<ProductUserShiftPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductUserShiftPO::getCompanyCode,SecurityUtil.getCompanySite())
                .eq(ProductUserShiftPO::getUserId,SecurityUtil.getUserId());
        List<ProductUserShiftPO> productShiftPOS = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(productShiftPOS)) {
            List<ProductShiftInVO.TeamUser> teamUsers = productShiftPOS.stream().map(productShiftPO -> {
                ProductShiftInVO.TeamUser teamUser = new ProductShiftInVO.TeamUser();
                teamUser.setUserId(productShiftPO.getTeamUsers());
                teamUser.setRoleCode(productShiftPO.getRoleCode());
                teamUser.setRoleName(productShiftPO.getRoleName());
                return teamUser;
            }).collect(Collectors.toList());
            productShiftInVO.setTeamUsers(teamUsers);
        }
        return productShiftInVO;
    }

    @Override
    public void updateProductShift(ProductShiftInVO productShiftInVO) {
//        productShiftInVO.setTeamUsers(productShiftInVO.getTeamUsers()
//                .stream().sorted(Comparator.comparing(ProductShiftInVO.TeamUser::getRoleCode))
//                .collect(Collectors.toList()));
//        List<Long> userIds = productShiftInVO.getTeamUsers().stream().map(teamUser -> Long.valueOf(teamUser.getUserId())).distinct().collect(Collectors.toList());
//        Map<Long, String> userMap =
//                ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
//        String teamUserName = productShiftInVO.getTeamUsers().stream().distinct().map(teamUser -> {
//            return userMap.get(Long.valueOf(teamUser.getUserId())) + "(" + teamUser.getRoleName() + ")";
//        }).collect(Collectors.joining(","));
//        productTicketPO.setTeamUsers(teamUserName);
//        productTicketService.updateById(productTicketPO);
//
//        // 更新班组信息
//        LambdaQueryWrapper<ProductShiftPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
//        lambdaQueryWrapper.in(ProductShiftPO::getProductTicketId,productShiftInVO.getProductTicketId())
//                .eq(ProductShiftPO::getDeleted, BooleanEnum.FALSE.getCode());
//        List<ProductShiftPO> productShiftPOS = list(lambdaQueryWrapper);
//
//        Map<String, ProductShiftPO> existingMap = productShiftPOS.stream()
//                .collect(Collectors.toMap(ProductShiftPO::getTeamUsers, vo -> vo));
//        for (ProductShiftInVO.TeamUser teamUser : productShiftInVO.getTeamUsers()) {
//            ProductShiftPO productShiftPO = existingMap.get(teamUser.getUserId());
//            if (productShiftPO == null) {
//                // 插入操作
//                productShiftPO = new ProductShiftPO();
//                productShiftPO.setCompanyCode(productTicketPO.getCompanyCode());
//                productShiftPO.setProductTicketId(productTicketPO.getId());
//                productShiftPO.setProduceDate(productTicketPO.getProduceDate());
//                productShiftPO.setShift(productTicketPO.getShift());
//                productShiftPO.setRoleCode(teamUser.getRoleCode());
//                productShiftPO.setRoleName(teamUser.getRoleName());
//                productShiftPO.setTeamUsers(teamUser.getUserId());
//                productShiftPO.setCreateBy(SecurityUtil.getUserId());
//                save(productShiftPO);
//            } else {
//                productShiftPO.setRoleName(teamUser.getRoleName());
//                productShiftPO.setRoleCode(teamUser.getRoleCode());
//                productShiftPO.setUpdateBy(SecurityUtil.getUserId());
//                updateById(productShiftPO);
//            }
//        }
//
//        // 查找需要删除的记录
//        for (ProductShiftPO productShiftPO : productShiftPOS) {
//            if (productShiftInVO.getTeamUsers().stream()
//                    .noneMatch(teamUser -> teamUser.getUserId().equals(productShiftPO.getTeamUsers()))) {
//                removeById(productShiftPO.getId());
//            }
//        }

    }
}
