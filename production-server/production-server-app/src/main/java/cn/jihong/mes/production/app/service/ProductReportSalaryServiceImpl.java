package cn.jihong.mes.production.app.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ReportSalaryInfoDTO;
import cn.jihong.mes.production.api.service.IProductReportSalaryService;
import cn.jihong.oa.erp.api.model.vo.in.ReportSalaryInfoInVO;
import cn.jihong.oa.erp.api.model.vo.out.ReportSalaryInfoOutVO;
import cn.jihong.oa.erp.api.service.ISffbTService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class ProductReportSalaryServiceImpl implements IProductReportSalaryService {

    @DubboReference
    private ISffbTService sffbTService;

    @Override
    public ReportSalaryInfoDTO getReportSalary() {
        ReportSalaryInfoInVO reportSalaryInfoInVO = new ReportSalaryInfoInVO();
        reportSalaryInfoInVO.setWorkCode(SecurityUtil.getWorkcode());
        Date date = new Date();
        DateTime begin = DateUtil.beginOfMonth(date);
        // 当月第一天
        reportSalaryInfoInVO.setReportStartDate(DateUtil.format(begin, DatePattern.NORM_DATE_PATTERN));
        reportSalaryInfoInVO.setReportEndDate(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        List<ReportSalaryInfoOutVO> reportSalaryInfo = sffbTService.getReportSalaryInfo(reportSalaryInfoInVO);

        // 数据封装
        ReportSalaryInfoDTO result = new ReportSalaryInfoDTO();
        result.setWorkCode(SecurityUtil.getWorkcode());

        if (CollectionUtil.isEmpty(reportSalaryInfo)) {
            result.setSumSalary(BigDecimal.ZERO);
            result.setReportSalaryDayInfoList(new ArrayList<>());
            return result;
        }

        // 按日期分组并汇总数据
        Map<String, ReportSalaryInfoDTO.ReportSalaryDayInfo> dayInfoMap = new LinkedHashMap<>();
        BigDecimal totalSumSalary = BigDecimal.ZERO;

        for (ReportSalaryInfoOutVO outVO : reportSalaryInfo) {
            String reportDate = outVO.getReportDate();

            // 获取或创建当日汇总信息
            ReportSalaryInfoDTO.ReportSalaryDayInfo dayInfo = dayInfoMap.computeIfAbsent(reportDate,
                k -> {
                    ReportSalaryInfoDTO.ReportSalaryDayInfo info = new ReportSalaryInfoDTO.ReportSalaryDayInfo();
                    info.setReportDate(k);
                    info.setTotalSalary(BigDecimal.ZERO);
                    info.setTotalReportQty(BigDecimal.ZERO);
                    info.setTotalReportQtyOfWages(BigDecimal.ZERO);
                    return info;
                });

            // 汇总当日数据
            if (CollectionUtil.isNotEmpty(outVO.getReportSalaryDayInfoList())) {
                for (ReportSalaryInfoOutVO.ReportSalaryDayInfo dailyInfo : outVO.getReportSalaryDayInfoList()) {
                    if (dailyInfo.getSalary() != null) {
                        dayInfo.setTotalSalary(dayInfo.getTotalSalary().add(dailyInfo.getSalary()));
                        totalSumSalary = totalSumSalary.add(dailyInfo.getSalary());
                    }
                    if (dailyInfo.getReportQty() != null) {
                        dayInfo.setTotalReportQty(dayInfo.getTotalReportQty().add(dailyInfo.getReportQty()));
                    }
                    if (dailyInfo.getReportQtyOfWages() != null) {
                        dayInfo.setTotalReportQtyOfWages(dayInfo.getTotalReportQtyOfWages().add(dailyInfo.getReportQtyOfWages()));
                    }
                }
            }
        }

        // 设置结果
        result.setSumSalary(totalSumSalary);
        result.setReportSalaryDayInfoList(new ArrayList<>(dayInfoMap.values()));

        return result;
    }
}
