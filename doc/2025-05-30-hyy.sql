CREATE TABLE `equipment_downtime` (
      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `company_code` varchar(50) NOT NULL COMMENT '工厂据点',
      `downtime_code` varchar(50) NOT NULL COMMENT '停机代码',
      `loss_type` varchar(50) NOT NULL COMMENT '损失类型',
      `code_type` int(2) NOT NULL COMMENT '代码类型: 1:通用  2:专用',
      `downtime_reason` varchar(500) DEFAULT NULL COMMENT '停机原因',
      `remark` varchar(200) NULL DEFAULT NULL COMMENT '备注',
      `create_by` bigint(0) NULL DEFAULT NULL COMMENT '创建人',
      `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
      `update_by` bigint(0) NULL DEFAULT NULL COMMENT '编辑人',
      `update_time` datetime(0) NULL DEFAULT NULL COMMENT '编辑时间',
      `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备停机代码表';


CREATE TABLE `equipment_downtime_process` (
       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
       `equipment_downtime_id` varchar(50) NOT NULL COMMENT '设备停机代码id',
       `process_code` varchar(50) NOT NULL COMMENT '工序编码',
       `process_name` varchar(50) NOT NULL COMMENT '工序名称',
       `create_by` bigint(0) NULL DEFAULT NULL COMMENT '创建人',
       `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
       `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除',
       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备停机工序关联表';


ALTER TABLE mes.product_machine_task ADD equipment_downtime_id BIGINT NULL COMMENT '设备停机id' AFTER type;
ALTER TABLE mes.product_machine_task ADD loss_type varchar(200) NULL COMMENT '损失类型' AFTER equipment_downtime_id;


ALTER TABLE mes.product_machine_task_history ADD equipment_downtime_id BIGINT NULL COMMENT '设备停机id' AFTER type;
ALTER TABLE mes.product_machine_task_history ADD loss_type varchar(200) NULL COMMENT '损失类型' AFTER equipment_downtime_id;

